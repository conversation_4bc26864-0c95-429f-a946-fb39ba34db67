/*
 * Stylesheet for Doxygen HTML output.
 *
 * This file defines styles for custom elements in the header/footer and
 * overrides some of the default Doxygen styles.
 *
 * Styles in this file do not affect the treeview sidebar.
 */

/* Set the margins to place a small amount of whitespace on the left and right
 * side of the page. */
div.contents {
	margin-left:4em;
	margin-right:4em;
}

/* Justify text in paragraphs. */
p {
	text-align: justify;
}

/* Style of section headings. */
h1 {
	border-bottom: 1px solid #879ECB;
	color: #354C7B;
	font-size: 160%;
	font-weight: normal;
	padding-bottom: 4px;
	padding-top: 8px;
}

/* Style of subsection headings. */
h2:not(.memtitle):not(.groupheader) {
	font-size: 125%;
	margin-bottom: 0px;
	margin-top: 16px;
	padding: 0px;
}

/* Style of paragraphs immediately after subsection headings. */
h2 + p {
	margin: 0px;
	padding: 0px;
}

/* Style of subsection headings. */
h3 {
	font-size: 100%;
	margin-bottom: 0px;
	margin-left: 2em;
	margin-right: 2em;
}

/* Style of paragraphs immediately after subsubsection headings. */
h3 + p {
	margin-top: 0px;
	margin-left: 2em;
	margin-right: 2em;
}

/* Style of the prefix "AWS IoT Device SDK C" that appears in the header. */
#csdkprefix {
	color: #757575;
}

/* Style of the "Return to main page" link that appears in the header. */
#returntomain {
	padding: 0.5em;
}

/* Style of the dividers on Configuration Settings pages. */
div.configpagedivider {
	margin-left: 0px !important;
	margin-right: 0px !important;
	margin-top: 20px !important;
}

/* Style of configuration setting names. */
dl.section.user ~ h1 {
	border-bottom: none;
	color: #000000;
	font-family: monospace, fixed;
	font-size: 16px;
	margin-bottom: 0px;
	margin-left: 2em;
	margin-top: 1.5em;
}

/* Style of paragraphs on a configuration settings page. */
dl.section.user ~ * {
	margin-bottom: 10px;
	margin-left: 4em;
	margin-right: 4em;
	margin-top: 0px;
}

/* Hide the configuration setting marker. */
dl.section.user {
	display: none;
}

/* Overrides for code fragments and lines. */
div.fragment {
	background: #ffffff;
	border: none;
	padding: 5px;
}

div.line {
	color: #3a3a3a;
}

/* Overrides for code syntax highlighting colors. */
span.comment {
	color: #008000;
}

span.keyword, span.keywordtype, span.keywordflow {
	color: #0000ff;
}

span.preprocessor {
	color: #50015a;
}

span.stringliteral, span.charliteral {
	color: #800c0c;
}

a.code, a.code:visited, a.line, a.line:visited {
	color: #496194;
}

div.image img[src="fleet_provisioning_operations.png"] {
    max-width: 1000px;
}
