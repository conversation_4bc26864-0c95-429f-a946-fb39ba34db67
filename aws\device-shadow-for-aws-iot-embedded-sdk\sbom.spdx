SPDXVersion: SPDX-2.2
DataLicense: CC0-1.0
SPDXID: SPDXRef-DOCUMENT
DocumentName: Device-Shadow-for-AWS-IoT-embedded-sdk
DocumentNamespace: https://github.com/FreeRTOS/Device-Shadow-for-AWS-IoT-embedded-sdk/blob/v1.3.0/sbom.spdx
Creator: Amazon Web Services
Created: 2022-10-14T16:03:17Z
CreatorComment: NOASSERTION
DocumentComment: NOASSERTION

PackageName: Device-Shadow-for-AWS-IoT-embedded-sdk
SPDXID: SPDXRef-Package-Device-Shadow-for-AWS-IoT-embedded-sdk
PackageVersion: v1.3.0
PackageDownloadLocation: https://github.com/aws/Device-Shadow-for-AWS-IoT-embedded-sdk/tree/v1.3.0
PackageLicenseConcluded: MIT
FilesAnalyzed: True
PackageVerificationCode: eea9cfc97b4c00c8b223b64bce009510c51de318
PackageCopyrightText: NOASSERTION
PackageSummary: NOASSERTION
PackageDescription: "Client library for using the AWS IoT Device Shadow service on embedded devices.\n"


FileName: ./shadow.c
SPDXID: SPDXRef-File-shadow.c
FileChecksum: SHA1: d5d54161800420ae72f7034c9f153a4b5c781e3c
LicenseConcluded: MIT
FileCopyrightText: NOASSERTION
FileComment: NOASSERTION

