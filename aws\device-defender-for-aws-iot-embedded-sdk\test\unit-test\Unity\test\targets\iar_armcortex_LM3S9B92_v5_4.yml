---
tools_root: C:\Program Files (x86)\IAR Systems\Embedded Workbench 5.4 Kickstart\
colour: true
:unity:
  :plugins: []
:tools:
  :test_compiler:
    :name: compiler
    :executable:
    - C:\Program Files (x86)\IAR Systems\Embedded Workbench 5.4 Kickstart\
    - arm\bin\iccarm.exe
    :arguments:
    - "--diag_suppress=Pa050"
    - "--debug"
    - "--endian=little"
    - "--cpu=Cortex-M3"
    - "--no_path_in_file_macros"
    - "-e"
    - "--fpu=None"
    - "--dlib_config"
    - - C:\Program Files (x86)\IAR Systems\Embedded Workbench 5.4 Kickstart\
      - arm\inc\DLib_Config_Normal.h
    - "--interwork"
    - "--warnings_are_errors"
    - "-Oh"
    - '-I"$": COLLECTION_PATHS_TEST_TOOLCHAIN_INCLUDE'
    - '-I"$": COLLECTION_PATHS_TEST_SUPPORT_SOURCE_INCLUDE_VENDOR'
    - "-D$: COLLECTION_DEFINES_TEST_AND_VENDOR"
    - "${1}"
    - "-o ${2}"
  :test_linker:
    :name: linker
    :executable:
    - C:\Program Files (x86)\IAR Systems\Embedded Workbench 5.4 Kickstart\
    - arm\bin\ilinkarm.exe
    :arguments:
    - "${1}"
    - "--redirect _Printf=_PrintfLarge"
    - "--redirect _Scanf=_ScanfSmall"
    - "--semihosting"
    - "--entry __iar_program_start"
    - "--config"
    - - C:\Program Files (x86)\IAR Systems\Embedded Workbench 5.4 Kickstart\
      - arm\config\generic.icf
    - "-o ${2}"
  :test_fixture:
    :name: simulator
    :executable:
    - C:\Program Files (x86)\IAR Systems\Embedded Workbench 5.4 Kickstart\
    - common\bin\CSpyBat.exe
    :arguments:
    - - C:\Program Files (x86)\IAR Systems\Embedded Workbench 5.4 Kickstart\
      - arm\bin\armproc.dll
    - - C:\Program Files (x86)\IAR Systems\Embedded Workbench 5.4 Kickstart\
      - arm\bin\armsim2.dll
    - "${1}"
    - "--plugin"
    - - C:\Program Files (x86)\IAR Systems\Embedded Workbench 5.4 Kickstart\
      - arm\bin\armbat.dll
    - "--backend"
    - "-B"
    - "--endian=little"
    - "--cpu=Cortex-M3"
    - "--fpu=None"
    - "-p"
    - - C:\Program Files (x86)\IAR Systems\Embedded Workbench 5.4 Kickstart\
      - arm\config\debugger\TexasInstruments\iolm3sxxxx.ddf
    - "--semihosting"
    - "--device=LM3SxBxx"
:extension:
  :object: ".r79"
  :executable: ".out"
:paths:
  :test:
    - - C:\Program Files (x86)\IAR Systems\Embedded Workbench 5.4 Kickstart\
      - arm\inc\
    - src\
    - "..\\src\\"
    - testdata/
    - tests\
    - vendor\unity\src\
    - iar\iar_v5\incIAR\
:defines:
  :test:
  - ewarm
  - PART_LM3S9B92
  - TARGET_IS_TEMPEST_RB1
  - USE_ROM_DRIVERS
  - UART_BUFFERED
  - UNITY_SUPPORT_64
