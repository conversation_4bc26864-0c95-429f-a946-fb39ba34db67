@startuml
skinparam dpi 300
skinparam classFontSize 8
skinparam classFontName Helvetica
autonumber

participant "User Application" as App
participant "Fleet Provisioning Library" as FleetProv
participant "MQTT Client" as MQTT
participant "JSON/CBOR Library" as <PERSON><PERSON><PERSON>

activate App

App -> MQTT : Connect to AWS IoT broker with claim credentials

activate MQTT
MQTT -> App : Connected
deactivate MQTT

opt If using CreateCertificateFromCsr
    App -> App : Securely generate keys and corresponding CSR

    App -> JSON : Generate CreateCertificateFromCsr request payload

    activate JSON
    JSON -> App : Request payload
    deactivate JSON

    App -> FleetProv : Get Accepted and Rejected Topic Strings\n for CreateCertificateFromCsr

    activate FleetProv
    FleetProv -> App : Accepted and Rejected Topic Strings
    deactivate FleetProv

    App -> MQTT : Subscribe to CreateCertificateFromCsr Accepted and Rejected Topics

    activate MQTT
    MQTT -> App : Subscription Successful
    deactivate MQTT

    App -> FleetProv : Get CreateCertificateFromCsr Publish Topic String

    activate FleetProv
    FleetProv -> App : Publish Topic String
    deactivate FleetProv

    App -> MQTT : Publish Request

    activate MQTT
    MQTT -> App : Published
    MQTT -> App : Incoming Publish Message Received
    deactivate MQTT

    App -> FleetProv : Is this a Fleet Provisioning CreateCertificateFromCsr\naccepted Message?

    activate FleetProv
    FleetProv -> App : Yes
    deactivate FleetProv

    App -> JSON : Parse response payload

    activate JSON
    JSON -> App : Certificate, certificate id, certificate ownership token
    deactivate JSON

    App -> App : Securely store certificate

else If using CreateKeysAndCertificate

    App -> FleetProv : Generate Accepted and Rejected Topic Strings for RegisterThing

    activate FleetProv
    FleetProv -> App : Accepted and Rejected Topic Strings
    deactivate FleetProv

    App -> MQTT : Subscribe to CreateKeysAndCertificate Accepted and Rejected Topics

    activate MQTT
    MQTT -> App : Subscription Successful
    deactivate MQTT

    App -> FleetProv : Get CreateKeysAndCertificate Publish Topic String

    activate FleetProv
    FleetProv -> App : Publish Topic String
    deactivate FleetProv

    App -> MQTT : Publish Request

    activate MQTT
    MQTT -> App : Published
    MQTT -> App : Incoming Publish Message Received
    deactivate MQTT

    App -> FleetProv : Is this a Fleet Provisioning CreateKeysAndCertificate\naccepted Message?

    activate FleetProv
    FleetProv -> App : Yes
    deactivate FleetProv

    App -> JSON : Parse response payload

    activate JSON
    JSON -> App : Private key, certificate, certificate id, certificate ownership token
    deactivate JSON

    App -> App : Securely store key and certificate

end

App -> JSON : Generate RegisterThing request payload

activate JSON
JSON -> App : Request payload
deactivate JSON
App -> FleetProv : Generate Accepted and Rejected Topic Strings for RegisterThing

activate FleetProv
FleetProv -> App : Accepted and Rejected Topic Strings
deactivate FleetProv

App -> MQTT : Subscribe to RegisterThing Accepted and Rejected Topics

activate MQTT
MQTT -> App : Subscription Successful
deactivate MQTT

App -> FleetProv : Get RegisterThing Publish Topic String

activate FleetProv
FleetProv -> App : Publish Topic String
deactivate FleetProv

App -> MQTT : Publish Request

activate MQTT
MQTT -> App : Published
MQTT -> App : Incoming Publish Message Received
deactivate MQTT

App -> FleetProv : Is this a Fleet Provisioning RegisterThing accepted Message?

activate FleetProv
FleetProv -> App : Yes
deactivate FleetProv

App -> JSON : Parse response payload

activate JSON
JSON -> App : Thing name, device configuration
deactivate JSON

App -> MQTT : Disconnect from AWS IoT broker

activate MQTT
MQTT -> App : Disconnected
deactivate MQTT

App -> MQTT : Connect to AWS IoT broker with newly provisioned credentials

activate MQTT
MQTT -> App : Connected
deactivate MQTT
deactivate App
@enduml
