---
:cmock:
  :plugins:
  - # none

:systest:
  :types: |
    #define UINT32 unsigned int

    typedef signed int custom_type;

  :mockable: |
    UINT32 foo(custom_type a);
    UINT32 bar(custom_type b);
    UINT32 foo_varargs(custom_type a, ...);
    const char* foo_char_strings(const char a[], const char* b);

  :source:
    :header: |
      UINT32 function_a(int a, int b);
      void function_b(void);
      UINT32 function_c(int a);
      const char* function_d(const char a[], const char* b);

    :code: |
      UINT32 function_a(int a, int b)
      {
        return foo((custom_type)a) + bar((custom_type)b);
      }

      void function_b(void) { }

      UINT32 function_c(int a)
      {
        return foo_varargs((custom_type)a, "ignored", 5);
      }

      const char* function_d(const char a[], const char* b)
      {
        return foo_char_strings(a, b);
      }

  :tests:
    :common: |
      void setUp(void) {}
      void tearDown(void) {}

    :units:
    - :pass: TRUE
      :should: 'successfully exercise two simple ExpectAndReturn mock calls'
      :code: |
        test()
        {
          foo_ExpectAndReturn((custom_type)1, 10);
          bar_ExpectAndReturn((custom_type)2, 20);
          TEST_ASSERT_EQUAL(30, function_a(1, 2));
        }

    - :pass: FALSE
      :should: 'fail because bar() is not called but is expected'
      :code: |
        test()
        {
          foo_ExpectAndReturn((custom_type)1, 10);
          TEST_ASSERT_EQUAL(30, function_a(1, 2));
        }

    - :pass: FALSE
      :should: 'fail because bar() is called but is not expected'
      :code: |
        test()
        {
          bar_ExpectAndReturn((custom_type)1, 10);
          function_b();
        }

    - :pass: TRUE
      :should: 'consume var args passed to mocked function'
      :code: |
        test()
        {
          foo_varargs_ExpectAndReturn((custom_type)3, 10);
          TEST_ASSERT_EQUAL(10, function_c(3));
        }

    - :pass: TRUE
      :should: 'handle char strings'
      :code: |
        test()
        {
          const char* retval = "moe";
          foo_char_strings_ExpectAndReturn("larry", "curly", (char*)retval);
          TEST_ASSERT_EQUAL_STRING("moe", function_d("larry", "curly"));
        }

    - :pass: TRUE
      :should: 'successfully exercise multiple cycles of expecting and mocking and pass'
      :code: |
        test()
        {
          foo_ExpectAndReturn((custom_type)1, 10);
          bar_ExpectAndReturn((custom_type)2, 20);
          TEST_ASSERT_EQUAL(30, function_a(1, 2));

          foo_ExpectAndReturn((custom_type)3, 30);
          bar_ExpectAndReturn((custom_type)4, 40);
          TEST_ASSERT_EQUAL(70, function_a(3, 4));
        }

    - :pass: FALSE
      :should: 'successfully exercise multiple cycles of expecting and mocking and fail'
      :code: |
        test()
        {
          foo_ExpectAndReturn((custom_type)1, 10);
          bar_ExpectAndReturn((custom_type)2, 20);
          TEST_ASSERT_EQUAL(30, function_a(1, 2));

          foo_ExpectAndReturn((custom_type)3, 30);
          bar_ExpectAndReturn((custom_type)4, 40);
          TEST_ASSERT_EQUAL(70, function_a(3, 5));
        }

...
