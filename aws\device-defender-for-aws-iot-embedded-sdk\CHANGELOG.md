# Changelog for AWS IoT Device Defender Library

## v1.3.0 (October 2022)

### Updates
 - [#60](https://github.com/aws/Device-Defender-for-AWS-IoT-embedded-sdk/pull/60) MISRA Compliance Update
 - [#59](https://github.com/aws/Device-Defender-for-AWS-IoT-embedded-sdk/pull/59) Update CBMC starter kit
 - [#56](https://github.com/aws/Device-Defender-for-AWS-IoT-embedded-sdk/pull/56) Loop invariant update


## v1.2.0 (November 2021)

### Updates
 - [#50](https://github.com/aws/Device-Defender-for-AWS-IoT-embedded-sdk/pull/50) Update doxygen version for documentation

## v1.1.1 (July 2021)

### Updates
 - [#45](https://github.com/aws/Device-Defender-for-AWS-IoT-embedded-sdk/pull/45) Remove parentheses from key name macros to enable concatenating them with other string literals.

## v1.1.0 (March 2021)

### Updates
 - [#36](https://github.com/aws/device-defender-for-aws-iot-embedded-sdk/pull/36) Add macros to API for [custom metrics](https://docs.aws.amazon.com/iot/latest/developerguide/dd-detect-custom-metrics.html) feature of AWS IoT Device Defender service.

## v1.0.1 (December 2020)

### Updates
 - [#28](https://github.com/aws/device-defender-for-aws-iot-embedded-sdk/pull/28) Cast logging arguments to C types matching the format specifiers.

### Other
 - [#23](https://github.com/aws/device-defender-for-aws-iot-embedded-sdk/pull/23) Formatting update.
 - [#25](https://github.com/aws/device-defender-for-aws-iot-embedded-sdk/pull/25), [#30](https://github.com/aws/device-defender-for-aws-iot-embedded-sdk/pull/30) Github actions updates.
 - [#26](https://github.com/aws/device-defender-for-aws-iot-embedded-sdk/pull/26), [#29](https://github.com/aws/device-defender-for-aws-iot-embedded-sdk/pull/29) Github repository chores.

## v1.0.0 (November 2020)

This is the first release of the AWS IoT Device Defender Client Library in this
repository.
