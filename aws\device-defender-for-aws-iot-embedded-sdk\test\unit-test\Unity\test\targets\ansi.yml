---
colour: true
:unity:
  :plugins: []
:skip_tests:
- :parameterized
:tools:
  :test_compiler:
    :name: compiler
    :executable: gcc
    :arguments:
    - "-c"
    - "-m64"
    - "-Wall"
    - "-Wno-address"
    - "-ansi"
    - '-I"$": COLLECTION_PATHS_TEST_TOOLCHAIN_INCLUDE'
    - '-I"$": COLLECTION_PATHS_TEST_SUPPORT_SOURCE_INCLUDE_VENDOR'
    - "-D$: COLLECTION_DEFINES_TEST_AND_VENDOR"
    - "${1}"
    - "-o ${2}"
  :test_linker:
    :name: linker
    :executable: gcc
    :arguments:
    - "${1}"
    - "-lm"
    - "-m64"
    - "-o ${2}"
:extension:
  :object: ".o"
  :executable: ".exe"
:paths:
  :test:
    - src/
    - "../src/"
    - testdata/
    - tests/
:defines:
  :test:
  - UNITY_INCLUDE_DOUBLE
  - UNITY_SUPPORT_TEST_CASES
  - UNITY_EXCLUDE_TESTING_NEW_COMMENTS
  - UNITY_SUPPORT_64
