# -*- mode: makefile -*-
# The first line sets the emacs major mode to Makefile

# Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.
# SPDX-License-Identifier: Apache-2.0

################################################################
# Use this file to give project-specific definitions of the command
# line arguments to pass to CBMC tools like goto-cc to build the goto
# binaries and cbmc to do the property and coverage checking.
#
# Use this file to override most default definitions of variables in
# Makefile.common.
################################################################

# Flags to pass to goto-cc for compilation (typically those passed to gcc -c)
COMPILE_FLAGS += -fPIC -std=gnu90

# Flags to pass to goto-cc for linking (typically those passed to gcc)
# LINK_FLAGS =

# Preprocessor include paths -I...
# Consider adding
#     INCLUDES += -I$(CBMC_ROOT)/include
# You will want to decide what order that comes in relative to the other
# include directories in your project.
#
INCLUDES += -I$(CBMC_ROOT)/include
INCLUDES += -I$(SRCDIR)/source/include
INCLUDES += -I$(SRCDIR)/test/include

# Preprocessor definitions -D...
# DEFINES =

# Path to arpa executable
# ARPA =

# Flags to pass to cmake for building the project
# ARPA_CMAKE_FLAGS =
