# ----------------------------------------------------------------------------
#          ATMEL Microcontroller Software Support  -  ROUSSET  -
# ----------------------------------------------------------------------------
#  DISCLAIMER:  THIS SOFTWARE IS PROVIDED BY ATMEL "AS IS" AND ANY EXPRESS OR
#  IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
#  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT ARE
#  DISCLAIMED. IN NO EVENT SHALL ATMEL BE LIABLE FOR ANY DIRECT, INDIRECT,
#  INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
#  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA,
#  OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
#  LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
#  NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
#  EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
# ----------------------------------------------------------------------------
# File Name           : AT91SAM7X256.h
# Object              : AT91SAM7X256 definitions
# Generated           : AT91 SW Application Group  11/02/2005 (15:17:24)
# 
# CVS Reference       : /AT91SAM7X256.pl/1.14/Tue Sep 13 15:06:52 2005//
# CVS Reference       : /SYS_SAM7X.pl/1.3/Tue Feb  1 17:01:43 2005//
# CVS Reference       : /MC_SAM7X.pl/1.2/Fri May 20 14:13:04 2005//
# CVS Reference       : /PMC_SAM7X.pl/1.4/Tue Feb  8 13:58:10 2005//
# CVS Reference       : /RSTC_SAM7X.pl/1.2/Wed Jul 13 14:57:50 2005//
# CVS Reference       : /UDP_SAM7X.pl/1.1/Tue May 10 11:35:35 2005//
# CVS Reference       : /PWM_SAM7X.pl/1.1/Tue May 10 11:53:07 2005//
# CVS Reference       : /AIC_6075B.pl/1.3/Fri May 20 14:01:30 2005//
# CVS Reference       : /PIO_6057A.pl/1.2/Thu Feb  3 10:18:28 2005//
# CVS Reference       : /RTTC_6081A.pl/1.2/Tue Nov  9 14:43:58 2004//
# CVS Reference       : /PITC_6079A.pl/1.2/Tue Nov  9 14:43:56 2004//
# CVS Reference       : /WDTC_6080A.pl/1.3/Tue Nov  9 14:44:00 2004//
# CVS Reference       : /VREG_6085B.pl/1.1/Tue Feb  1 16:05:48 2005//
# CVS Reference       : /PDC_6074C.pl/1.2/Thu Feb  3 08:48:54 2005//
# CVS Reference       : /DBGU_6059D.pl/1.1/Mon Jan 31 13:15:32 2005//
# CVS Reference       : /SPI_6088D.pl/1.3/Fri May 20 14:08:59 2005//
# CVS Reference       : /US_6089C.pl/1.1/Mon Jul 12 18:23:26 2004//
# CVS Reference       : /SSC_6078B.pl/1.1/Wed Jul 13 15:19:19 2005//
# CVS Reference       : /TWI_6061A.pl/1.1/Tue Jul 13 07:38:06 2004//
# CVS Reference       : /TC_6082A.pl/1.7/Fri Mar 11 12:52:17 2005//
# CVS Reference       : /CAN_6019B.pl/1.1/Tue Mar  8 12:42:22 2005//
# CVS Reference       : /EMACB_6119A.pl/1.6/Wed Jul 13 15:05:35 2005//
# CVS Reference       : /ADC_6051C.pl/1.1/Fri Oct 17 09:12:38 2003//
# ----------------------------------------------------------------------------

rdf.version=1

~sysinclude=arm_default.rdf
~sysinclude=arm_status.rdf
# ========== Register definition for SYS peripheral ========== 
# ========== Register definition for AIC peripheral ========== 
AT91C_AIC_IVR.name="AT91C_AIC_IVR"
AT91C_AIC_IVR.description="IRQ Vector Register"
AT91C_AIC_IVR.helpkey="IRQ Vector Register"
AT91C_AIC_IVR.access=memorymapped
AT91C_AIC_IVR.address=0xFFFFF100
AT91C_AIC_IVR.width=32
AT91C_AIC_IVR.byteEndian=little
AT91C_AIC_IVR.permission.write=none
AT91C_AIC_SMR.name="AT91C_AIC_SMR"
AT91C_AIC_SMR.description="Source Mode Register"
AT91C_AIC_SMR.helpkey="Source Mode Register"
AT91C_AIC_SMR.access=memorymapped
AT91C_AIC_SMR.address=0xFFFFF000
AT91C_AIC_SMR.width=32
AT91C_AIC_SMR.byteEndian=little
AT91C_AIC_FVR.name="AT91C_AIC_FVR"
AT91C_AIC_FVR.description="FIQ Vector Register"
AT91C_AIC_FVR.helpkey="FIQ Vector Register"
AT91C_AIC_FVR.access=memorymapped
AT91C_AIC_FVR.address=0xFFFFF104
AT91C_AIC_FVR.width=32
AT91C_AIC_FVR.byteEndian=little
AT91C_AIC_FVR.permission.write=none
AT91C_AIC_DCR.name="AT91C_AIC_DCR"
AT91C_AIC_DCR.description="Debug Control Register (Protect)"
AT91C_AIC_DCR.helpkey="Debug Control Register (Protect)"
AT91C_AIC_DCR.access=memorymapped
AT91C_AIC_DCR.address=0xFFFFF138
AT91C_AIC_DCR.width=32
AT91C_AIC_DCR.byteEndian=little
AT91C_AIC_EOICR.name="AT91C_AIC_EOICR"
AT91C_AIC_EOICR.description="End of Interrupt Command Register"
AT91C_AIC_EOICR.helpkey="End of Interrupt Command Register"
AT91C_AIC_EOICR.access=memorymapped
AT91C_AIC_EOICR.address=0xFFFFF130
AT91C_AIC_EOICR.width=32
AT91C_AIC_EOICR.byteEndian=little
AT91C_AIC_EOICR.type=enum
AT91C_AIC_EOICR.enum.0.name=*** Write only ***
AT91C_AIC_EOICR.enum.1.name=Error
AT91C_AIC_SVR.name="AT91C_AIC_SVR"
AT91C_AIC_SVR.description="Source Vector Register"
AT91C_AIC_SVR.helpkey="Source Vector Register"
AT91C_AIC_SVR.access=memorymapped
AT91C_AIC_SVR.address=0xFFFFF080
AT91C_AIC_SVR.width=32
AT91C_AIC_SVR.byteEndian=little
AT91C_AIC_FFSR.name="AT91C_AIC_FFSR"
AT91C_AIC_FFSR.description="Fast Forcing Status Register"
AT91C_AIC_FFSR.helpkey="Fast Forcing Status Register"
AT91C_AIC_FFSR.access=memorymapped
AT91C_AIC_FFSR.address=0xFFFFF148
AT91C_AIC_FFSR.width=32
AT91C_AIC_FFSR.byteEndian=little
AT91C_AIC_FFSR.permission.write=none
AT91C_AIC_ICCR.name="AT91C_AIC_ICCR"
AT91C_AIC_ICCR.description="Interrupt Clear Command Register"
AT91C_AIC_ICCR.helpkey="Interrupt Clear Command Register"
AT91C_AIC_ICCR.access=memorymapped
AT91C_AIC_ICCR.address=0xFFFFF128
AT91C_AIC_ICCR.width=32
AT91C_AIC_ICCR.byteEndian=little
AT91C_AIC_ICCR.type=enum
AT91C_AIC_ICCR.enum.0.name=*** Write only ***
AT91C_AIC_ICCR.enum.1.name=Error
AT91C_AIC_ISR.name="AT91C_AIC_ISR"
AT91C_AIC_ISR.description="Interrupt Status Register"
AT91C_AIC_ISR.helpkey="Interrupt Status Register"
AT91C_AIC_ISR.access=memorymapped
AT91C_AIC_ISR.address=0xFFFFF108
AT91C_AIC_ISR.width=32
AT91C_AIC_ISR.byteEndian=little
AT91C_AIC_ISR.permission.write=none
AT91C_AIC_IMR.name="AT91C_AIC_IMR"
AT91C_AIC_IMR.description="Interrupt Mask Register"
AT91C_AIC_IMR.helpkey="Interrupt Mask Register"
AT91C_AIC_IMR.access=memorymapped
AT91C_AIC_IMR.address=0xFFFFF110
AT91C_AIC_IMR.width=32
AT91C_AIC_IMR.byteEndian=little
AT91C_AIC_IMR.permission.write=none
AT91C_AIC_IPR.name="AT91C_AIC_IPR"
AT91C_AIC_IPR.description="Interrupt Pending Register"
AT91C_AIC_IPR.helpkey="Interrupt Pending Register"
AT91C_AIC_IPR.access=memorymapped
AT91C_AIC_IPR.address=0xFFFFF10C
AT91C_AIC_IPR.width=32
AT91C_AIC_IPR.byteEndian=little
AT91C_AIC_IPR.permission.write=none
AT91C_AIC_FFER.name="AT91C_AIC_FFER"
AT91C_AIC_FFER.description="Fast Forcing Enable Register"
AT91C_AIC_FFER.helpkey="Fast Forcing Enable Register"
AT91C_AIC_FFER.access=memorymapped
AT91C_AIC_FFER.address=0xFFFFF140
AT91C_AIC_FFER.width=32
AT91C_AIC_FFER.byteEndian=little
AT91C_AIC_FFER.type=enum
AT91C_AIC_FFER.enum.0.name=*** Write only ***
AT91C_AIC_FFER.enum.1.name=Error
AT91C_AIC_IECR.name="AT91C_AIC_IECR"
AT91C_AIC_IECR.description="Interrupt Enable Command Register"
AT91C_AIC_IECR.helpkey="Interrupt Enable Command Register"
AT91C_AIC_IECR.access=memorymapped
AT91C_AIC_IECR.address=0xFFFFF120
AT91C_AIC_IECR.width=32
AT91C_AIC_IECR.byteEndian=little
AT91C_AIC_IECR.type=enum
AT91C_AIC_IECR.enum.0.name=*** Write only ***
AT91C_AIC_IECR.enum.1.name=Error
AT91C_AIC_ISCR.name="AT91C_AIC_ISCR"
AT91C_AIC_ISCR.description="Interrupt Set Command Register"
AT91C_AIC_ISCR.helpkey="Interrupt Set Command Register"
AT91C_AIC_ISCR.access=memorymapped
AT91C_AIC_ISCR.address=0xFFFFF12C
AT91C_AIC_ISCR.width=32
AT91C_AIC_ISCR.byteEndian=little
AT91C_AIC_ISCR.type=enum
AT91C_AIC_ISCR.enum.0.name=*** Write only ***
AT91C_AIC_ISCR.enum.1.name=Error
AT91C_AIC_FFDR.name="AT91C_AIC_FFDR"
AT91C_AIC_FFDR.description="Fast Forcing Disable Register"
AT91C_AIC_FFDR.helpkey="Fast Forcing Disable Register"
AT91C_AIC_FFDR.access=memorymapped
AT91C_AIC_FFDR.address=0xFFFFF144
AT91C_AIC_FFDR.width=32
AT91C_AIC_FFDR.byteEndian=little
AT91C_AIC_FFDR.type=enum
AT91C_AIC_FFDR.enum.0.name=*** Write only ***
AT91C_AIC_FFDR.enum.1.name=Error
AT91C_AIC_CISR.name="AT91C_AIC_CISR"
AT91C_AIC_CISR.description="Core Interrupt Status Register"
AT91C_AIC_CISR.helpkey="Core Interrupt Status Register"
AT91C_AIC_CISR.access=memorymapped
AT91C_AIC_CISR.address=0xFFFFF114
AT91C_AIC_CISR.width=32
AT91C_AIC_CISR.byteEndian=little
AT91C_AIC_CISR.permission.write=none
AT91C_AIC_IDCR.name="AT91C_AIC_IDCR"
AT91C_AIC_IDCR.description="Interrupt Disable Command Register"
AT91C_AIC_IDCR.helpkey="Interrupt Disable Command Register"
AT91C_AIC_IDCR.access=memorymapped
AT91C_AIC_IDCR.address=0xFFFFF124
AT91C_AIC_IDCR.width=32
AT91C_AIC_IDCR.byteEndian=little
AT91C_AIC_IDCR.type=enum
AT91C_AIC_IDCR.enum.0.name=*** Write only ***
AT91C_AIC_IDCR.enum.1.name=Error
AT91C_AIC_SPU.name="AT91C_AIC_SPU"
AT91C_AIC_SPU.description="Spurious Vector Register"
AT91C_AIC_SPU.helpkey="Spurious Vector Register"
AT91C_AIC_SPU.access=memorymapped
AT91C_AIC_SPU.address=0xFFFFF134
AT91C_AIC_SPU.width=32
AT91C_AIC_SPU.byteEndian=little
# ========== Register definition for PDC_DBGU peripheral ========== 
AT91C_DBGU_TCR.name="AT91C_DBGU_TCR"
AT91C_DBGU_TCR.description="Transmit Counter Register"
AT91C_DBGU_TCR.helpkey="Transmit Counter Register"
AT91C_DBGU_TCR.access=memorymapped
AT91C_DBGU_TCR.address=0xFFFFF30C
AT91C_DBGU_TCR.width=32
AT91C_DBGU_TCR.byteEndian=little
AT91C_DBGU_RNPR.name="AT91C_DBGU_RNPR"
AT91C_DBGU_RNPR.description="Receive Next Pointer Register"
AT91C_DBGU_RNPR.helpkey="Receive Next Pointer Register"
AT91C_DBGU_RNPR.access=memorymapped
AT91C_DBGU_RNPR.address=0xFFFFF310
AT91C_DBGU_RNPR.width=32
AT91C_DBGU_RNPR.byteEndian=little
AT91C_DBGU_TNPR.name="AT91C_DBGU_TNPR"
AT91C_DBGU_TNPR.description="Transmit Next Pointer Register"
AT91C_DBGU_TNPR.helpkey="Transmit Next Pointer Register"
AT91C_DBGU_TNPR.access=memorymapped
AT91C_DBGU_TNPR.address=0xFFFFF318
AT91C_DBGU_TNPR.width=32
AT91C_DBGU_TNPR.byteEndian=little
AT91C_DBGU_TPR.name="AT91C_DBGU_TPR"
AT91C_DBGU_TPR.description="Transmit Pointer Register"
AT91C_DBGU_TPR.helpkey="Transmit Pointer Register"
AT91C_DBGU_TPR.access=memorymapped
AT91C_DBGU_TPR.address=0xFFFFF308
AT91C_DBGU_TPR.width=32
AT91C_DBGU_TPR.byteEndian=little
AT91C_DBGU_RPR.name="AT91C_DBGU_RPR"
AT91C_DBGU_RPR.description="Receive Pointer Register"
AT91C_DBGU_RPR.helpkey="Receive Pointer Register"
AT91C_DBGU_RPR.access=memorymapped
AT91C_DBGU_RPR.address=0xFFFFF300
AT91C_DBGU_RPR.width=32
AT91C_DBGU_RPR.byteEndian=little
AT91C_DBGU_RCR.name="AT91C_DBGU_RCR"
AT91C_DBGU_RCR.description="Receive Counter Register"
AT91C_DBGU_RCR.helpkey="Receive Counter Register"
AT91C_DBGU_RCR.access=memorymapped
AT91C_DBGU_RCR.address=0xFFFFF304
AT91C_DBGU_RCR.width=32
AT91C_DBGU_RCR.byteEndian=little
AT91C_DBGU_RNCR.name="AT91C_DBGU_RNCR"
AT91C_DBGU_RNCR.description="Receive Next Counter Register"
AT91C_DBGU_RNCR.helpkey="Receive Next Counter Register"
AT91C_DBGU_RNCR.access=memorymapped
AT91C_DBGU_RNCR.address=0xFFFFF314
AT91C_DBGU_RNCR.width=32
AT91C_DBGU_RNCR.byteEndian=little
AT91C_DBGU_PTCR.name="AT91C_DBGU_PTCR"
AT91C_DBGU_PTCR.description="PDC Transfer Control Register"
AT91C_DBGU_PTCR.helpkey="PDC Transfer Control Register"
AT91C_DBGU_PTCR.access=memorymapped
AT91C_DBGU_PTCR.address=0xFFFFF320
AT91C_DBGU_PTCR.width=32
AT91C_DBGU_PTCR.byteEndian=little
AT91C_DBGU_PTCR.type=enum
AT91C_DBGU_PTCR.enum.0.name=*** Write only ***
AT91C_DBGU_PTCR.enum.1.name=Error
AT91C_DBGU_PTSR.name="AT91C_DBGU_PTSR"
AT91C_DBGU_PTSR.description="PDC Transfer Status Register"
AT91C_DBGU_PTSR.helpkey="PDC Transfer Status Register"
AT91C_DBGU_PTSR.access=memorymapped
AT91C_DBGU_PTSR.address=0xFFFFF324
AT91C_DBGU_PTSR.width=32
AT91C_DBGU_PTSR.byteEndian=little
AT91C_DBGU_PTSR.permission.write=none
AT91C_DBGU_TNCR.name="AT91C_DBGU_TNCR"
AT91C_DBGU_TNCR.description="Transmit Next Counter Register"
AT91C_DBGU_TNCR.helpkey="Transmit Next Counter Register"
AT91C_DBGU_TNCR.access=memorymapped
AT91C_DBGU_TNCR.address=0xFFFFF31C
AT91C_DBGU_TNCR.width=32
AT91C_DBGU_TNCR.byteEndian=little
# ========== Register definition for DBGU peripheral ========== 
AT91C_DBGU_EXID.name="AT91C_DBGU_EXID"
AT91C_DBGU_EXID.description="Chip ID Extension Register"
AT91C_DBGU_EXID.helpkey="Chip ID Extension Register"
AT91C_DBGU_EXID.access=memorymapped
AT91C_DBGU_EXID.address=0xFFFFF244
AT91C_DBGU_EXID.width=32
AT91C_DBGU_EXID.byteEndian=little
AT91C_DBGU_EXID.permission.write=none
AT91C_DBGU_BRGR.name="AT91C_DBGU_BRGR"
AT91C_DBGU_BRGR.description="Baud Rate Generator Register"
AT91C_DBGU_BRGR.helpkey="Baud Rate Generator Register"
AT91C_DBGU_BRGR.access=memorymapped
AT91C_DBGU_BRGR.address=0xFFFFF220
AT91C_DBGU_BRGR.width=32
AT91C_DBGU_BRGR.byteEndian=little
AT91C_DBGU_IDR.name="AT91C_DBGU_IDR"
AT91C_DBGU_IDR.description="Interrupt Disable Register"
AT91C_DBGU_IDR.helpkey="Interrupt Disable Register"
AT91C_DBGU_IDR.access=memorymapped
AT91C_DBGU_IDR.address=0xFFFFF20C
AT91C_DBGU_IDR.width=32
AT91C_DBGU_IDR.byteEndian=little
AT91C_DBGU_IDR.type=enum
AT91C_DBGU_IDR.enum.0.name=*** Write only ***
AT91C_DBGU_IDR.enum.1.name=Error
AT91C_DBGU_CSR.name="AT91C_DBGU_CSR"
AT91C_DBGU_CSR.description="Channel Status Register"
AT91C_DBGU_CSR.helpkey="Channel Status Register"
AT91C_DBGU_CSR.access=memorymapped
AT91C_DBGU_CSR.address=0xFFFFF214
AT91C_DBGU_CSR.width=32
AT91C_DBGU_CSR.byteEndian=little
AT91C_DBGU_CSR.permission.write=none
AT91C_DBGU_CIDR.name="AT91C_DBGU_CIDR"
AT91C_DBGU_CIDR.description="Chip ID Register"
AT91C_DBGU_CIDR.helpkey="Chip ID Register"
AT91C_DBGU_CIDR.access=memorymapped
AT91C_DBGU_CIDR.address=0xFFFFF240
AT91C_DBGU_CIDR.width=32
AT91C_DBGU_CIDR.byteEndian=little
AT91C_DBGU_CIDR.permission.write=none
AT91C_DBGU_MR.name="AT91C_DBGU_MR"
AT91C_DBGU_MR.description="Mode Register"
AT91C_DBGU_MR.helpkey="Mode Register"
AT91C_DBGU_MR.access=memorymapped
AT91C_DBGU_MR.address=0xFFFFF204
AT91C_DBGU_MR.width=32
AT91C_DBGU_MR.byteEndian=little
AT91C_DBGU_IMR.name="AT91C_DBGU_IMR"
AT91C_DBGU_IMR.description="Interrupt Mask Register"
AT91C_DBGU_IMR.helpkey="Interrupt Mask Register"
AT91C_DBGU_IMR.access=memorymapped
AT91C_DBGU_IMR.address=0xFFFFF210
AT91C_DBGU_IMR.width=32
AT91C_DBGU_IMR.byteEndian=little
AT91C_DBGU_IMR.permission.write=none
AT91C_DBGU_CR.name="AT91C_DBGU_CR"
AT91C_DBGU_CR.description="Control Register"
AT91C_DBGU_CR.helpkey="Control Register"
AT91C_DBGU_CR.access=memorymapped
AT91C_DBGU_CR.address=0xFFFFF200
AT91C_DBGU_CR.width=32
AT91C_DBGU_CR.byteEndian=little
AT91C_DBGU_CR.type=enum
AT91C_DBGU_CR.enum.0.name=*** Write only ***
AT91C_DBGU_CR.enum.1.name=Error
AT91C_DBGU_FNTR.name="AT91C_DBGU_FNTR"
AT91C_DBGU_FNTR.description="Force NTRST Register"
AT91C_DBGU_FNTR.helpkey="Force NTRST Register"
AT91C_DBGU_FNTR.access=memorymapped
AT91C_DBGU_FNTR.address=0xFFFFF248
AT91C_DBGU_FNTR.width=32
AT91C_DBGU_FNTR.byteEndian=little
AT91C_DBGU_THR.name="AT91C_DBGU_THR"
AT91C_DBGU_THR.description="Transmitter Holding Register"
AT91C_DBGU_THR.helpkey="Transmitter Holding Register"
AT91C_DBGU_THR.access=memorymapped
AT91C_DBGU_THR.address=0xFFFFF21C
AT91C_DBGU_THR.width=32
AT91C_DBGU_THR.byteEndian=little
AT91C_DBGU_THR.type=enum
AT91C_DBGU_THR.enum.0.name=*** Write only ***
AT91C_DBGU_THR.enum.1.name=Error
AT91C_DBGU_RHR.name="AT91C_DBGU_RHR"
AT91C_DBGU_RHR.description="Receiver Holding Register"
AT91C_DBGU_RHR.helpkey="Receiver Holding Register"
AT91C_DBGU_RHR.access=memorymapped
AT91C_DBGU_RHR.address=0xFFFFF218
AT91C_DBGU_RHR.width=32
AT91C_DBGU_RHR.byteEndian=little
AT91C_DBGU_RHR.permission.write=none
AT91C_DBGU_IER.name="AT91C_DBGU_IER"
AT91C_DBGU_IER.description="Interrupt Enable Register"
AT91C_DBGU_IER.helpkey="Interrupt Enable Register"
AT91C_DBGU_IER.access=memorymapped
AT91C_DBGU_IER.address=0xFFFFF208
AT91C_DBGU_IER.width=32
AT91C_DBGU_IER.byteEndian=little
AT91C_DBGU_IER.type=enum
AT91C_DBGU_IER.enum.0.name=*** Write only ***
AT91C_DBGU_IER.enum.1.name=Error
# ========== Register definition for PIOA peripheral ========== 
AT91C_PIOA_ODR.name="AT91C_PIOA_ODR"
AT91C_PIOA_ODR.description="Output Disable Registerr"
AT91C_PIOA_ODR.helpkey="Output Disable Registerr"
AT91C_PIOA_ODR.access=memorymapped
AT91C_PIOA_ODR.address=0xFFFFF414
AT91C_PIOA_ODR.width=32
AT91C_PIOA_ODR.byteEndian=little
AT91C_PIOA_ODR.type=enum
AT91C_PIOA_ODR.enum.0.name=*** Write only ***
AT91C_PIOA_ODR.enum.1.name=Error
AT91C_PIOA_SODR.name="AT91C_PIOA_SODR"
AT91C_PIOA_SODR.description="Set Output Data Register"
AT91C_PIOA_SODR.helpkey="Set Output Data Register"
AT91C_PIOA_SODR.access=memorymapped
AT91C_PIOA_SODR.address=0xFFFFF430
AT91C_PIOA_SODR.width=32
AT91C_PIOA_SODR.byteEndian=little
AT91C_PIOA_SODR.type=enum
AT91C_PIOA_SODR.enum.0.name=*** Write only ***
AT91C_PIOA_SODR.enum.1.name=Error
AT91C_PIOA_ISR.name="AT91C_PIOA_ISR"
AT91C_PIOA_ISR.description="Interrupt Status Register"
AT91C_PIOA_ISR.helpkey="Interrupt Status Register"
AT91C_PIOA_ISR.access=memorymapped
AT91C_PIOA_ISR.address=0xFFFFF44C
AT91C_PIOA_ISR.width=32
AT91C_PIOA_ISR.byteEndian=little
AT91C_PIOA_ISR.permission.write=none
AT91C_PIOA_ABSR.name="AT91C_PIOA_ABSR"
AT91C_PIOA_ABSR.description="AB Select Status Register"
AT91C_PIOA_ABSR.helpkey="AB Select Status Register"
AT91C_PIOA_ABSR.access=memorymapped
AT91C_PIOA_ABSR.address=0xFFFFF478
AT91C_PIOA_ABSR.width=32
AT91C_PIOA_ABSR.byteEndian=little
AT91C_PIOA_ABSR.permission.write=none
AT91C_PIOA_IER.name="AT91C_PIOA_IER"
AT91C_PIOA_IER.description="Interrupt Enable Register"
AT91C_PIOA_IER.helpkey="Interrupt Enable Register"
AT91C_PIOA_IER.access=memorymapped
AT91C_PIOA_IER.address=0xFFFFF440
AT91C_PIOA_IER.width=32
AT91C_PIOA_IER.byteEndian=little
AT91C_PIOA_IER.type=enum
AT91C_PIOA_IER.enum.0.name=*** Write only ***
AT91C_PIOA_IER.enum.1.name=Error
AT91C_PIOA_PPUDR.name="AT91C_PIOA_PPUDR"
AT91C_PIOA_PPUDR.description="Pull-up Disable Register"
AT91C_PIOA_PPUDR.helpkey="Pull-up Disable Register"
AT91C_PIOA_PPUDR.access=memorymapped
AT91C_PIOA_PPUDR.address=0xFFFFF460
AT91C_PIOA_PPUDR.width=32
AT91C_PIOA_PPUDR.byteEndian=little
AT91C_PIOA_PPUDR.type=enum
AT91C_PIOA_PPUDR.enum.0.name=*** Write only ***
AT91C_PIOA_PPUDR.enum.1.name=Error
AT91C_PIOA_IMR.name="AT91C_PIOA_IMR"
AT91C_PIOA_IMR.description="Interrupt Mask Register"
AT91C_PIOA_IMR.helpkey="Interrupt Mask Register"
AT91C_PIOA_IMR.access=memorymapped
AT91C_PIOA_IMR.address=0xFFFFF448
AT91C_PIOA_IMR.width=32
AT91C_PIOA_IMR.byteEndian=little
AT91C_PIOA_IMR.permission.write=none
AT91C_PIOA_PER.name="AT91C_PIOA_PER"
AT91C_PIOA_PER.description="PIO Enable Register"
AT91C_PIOA_PER.helpkey="PIO Enable Register"
AT91C_PIOA_PER.access=memorymapped
AT91C_PIOA_PER.address=0xFFFFF400
AT91C_PIOA_PER.width=32
AT91C_PIOA_PER.byteEndian=little
AT91C_PIOA_PER.type=enum
AT91C_PIOA_PER.enum.0.name=*** Write only ***
AT91C_PIOA_PER.enum.1.name=Error
AT91C_PIOA_IFDR.name="AT91C_PIOA_IFDR"
AT91C_PIOA_IFDR.description="Input Filter Disable Register"
AT91C_PIOA_IFDR.helpkey="Input Filter Disable Register"
AT91C_PIOA_IFDR.access=memorymapped
AT91C_PIOA_IFDR.address=0xFFFFF424
AT91C_PIOA_IFDR.width=32
AT91C_PIOA_IFDR.byteEndian=little
AT91C_PIOA_IFDR.type=enum
AT91C_PIOA_IFDR.enum.0.name=*** Write only ***
AT91C_PIOA_IFDR.enum.1.name=Error
AT91C_PIOA_OWDR.name="AT91C_PIOA_OWDR"
AT91C_PIOA_OWDR.description="Output Write Disable Register"
AT91C_PIOA_OWDR.helpkey="Output Write Disable Register"
AT91C_PIOA_OWDR.access=memorymapped
AT91C_PIOA_OWDR.address=0xFFFFF4A4
AT91C_PIOA_OWDR.width=32
AT91C_PIOA_OWDR.byteEndian=little
AT91C_PIOA_OWDR.type=enum
AT91C_PIOA_OWDR.enum.0.name=*** Write only ***
AT91C_PIOA_OWDR.enum.1.name=Error
AT91C_PIOA_MDSR.name="AT91C_PIOA_MDSR"
AT91C_PIOA_MDSR.description="Multi-driver Status Register"
AT91C_PIOA_MDSR.helpkey="Multi-driver Status Register"
AT91C_PIOA_MDSR.access=memorymapped
AT91C_PIOA_MDSR.address=0xFFFFF458
AT91C_PIOA_MDSR.width=32
AT91C_PIOA_MDSR.byteEndian=little
AT91C_PIOA_MDSR.permission.write=none
AT91C_PIOA_IDR.name="AT91C_PIOA_IDR"
AT91C_PIOA_IDR.description="Interrupt Disable Register"
AT91C_PIOA_IDR.helpkey="Interrupt Disable Register"
AT91C_PIOA_IDR.access=memorymapped
AT91C_PIOA_IDR.address=0xFFFFF444
AT91C_PIOA_IDR.width=32
AT91C_PIOA_IDR.byteEndian=little
AT91C_PIOA_IDR.type=enum
AT91C_PIOA_IDR.enum.0.name=*** Write only ***
AT91C_PIOA_IDR.enum.1.name=Error
AT91C_PIOA_ODSR.name="AT91C_PIOA_ODSR"
AT91C_PIOA_ODSR.description="Output Data Status Register"
AT91C_PIOA_ODSR.helpkey="Output Data Status Register"
AT91C_PIOA_ODSR.access=memorymapped
AT91C_PIOA_ODSR.address=0xFFFFF438
AT91C_PIOA_ODSR.width=32
AT91C_PIOA_ODSR.byteEndian=little
AT91C_PIOA_ODSR.permission.write=none
AT91C_PIOA_PPUSR.name="AT91C_PIOA_PPUSR"
AT91C_PIOA_PPUSR.description="Pull-up Status Register"
AT91C_PIOA_PPUSR.helpkey="Pull-up Status Register"
AT91C_PIOA_PPUSR.access=memorymapped
AT91C_PIOA_PPUSR.address=0xFFFFF468
AT91C_PIOA_PPUSR.width=32
AT91C_PIOA_PPUSR.byteEndian=little
AT91C_PIOA_PPUSR.permission.write=none
AT91C_PIOA_OWSR.name="AT91C_PIOA_OWSR"
AT91C_PIOA_OWSR.description="Output Write Status Register"
AT91C_PIOA_OWSR.helpkey="Output Write Status Register"
AT91C_PIOA_OWSR.access=memorymapped
AT91C_PIOA_OWSR.address=0xFFFFF4A8
AT91C_PIOA_OWSR.width=32
AT91C_PIOA_OWSR.byteEndian=little
AT91C_PIOA_OWSR.permission.write=none
AT91C_PIOA_BSR.name="AT91C_PIOA_BSR"
AT91C_PIOA_BSR.description="Select B Register"
AT91C_PIOA_BSR.helpkey="Select B Register"
AT91C_PIOA_BSR.access=memorymapped
AT91C_PIOA_BSR.address=0xFFFFF474
AT91C_PIOA_BSR.width=32
AT91C_PIOA_BSR.byteEndian=little
AT91C_PIOA_BSR.type=enum
AT91C_PIOA_BSR.enum.0.name=*** Write only ***
AT91C_PIOA_BSR.enum.1.name=Error
AT91C_PIOA_OWER.name="AT91C_PIOA_OWER"
AT91C_PIOA_OWER.description="Output Write Enable Register"
AT91C_PIOA_OWER.helpkey="Output Write Enable Register"
AT91C_PIOA_OWER.access=memorymapped
AT91C_PIOA_OWER.address=0xFFFFF4A0
AT91C_PIOA_OWER.width=32
AT91C_PIOA_OWER.byteEndian=little
AT91C_PIOA_OWER.type=enum
AT91C_PIOA_OWER.enum.0.name=*** Write only ***
AT91C_PIOA_OWER.enum.1.name=Error
AT91C_PIOA_IFER.name="AT91C_PIOA_IFER"
AT91C_PIOA_IFER.description="Input Filter Enable Register"
AT91C_PIOA_IFER.helpkey="Input Filter Enable Register"
AT91C_PIOA_IFER.access=memorymapped
AT91C_PIOA_IFER.address=0xFFFFF420
AT91C_PIOA_IFER.width=32
AT91C_PIOA_IFER.byteEndian=little
AT91C_PIOA_IFER.type=enum
AT91C_PIOA_IFER.enum.0.name=*** Write only ***
AT91C_PIOA_IFER.enum.1.name=Error
AT91C_PIOA_PDSR.name="AT91C_PIOA_PDSR"
AT91C_PIOA_PDSR.description="Pin Data Status Register"
AT91C_PIOA_PDSR.helpkey="Pin Data Status Register"
AT91C_PIOA_PDSR.access=memorymapped
AT91C_PIOA_PDSR.address=0xFFFFF43C
AT91C_PIOA_PDSR.width=32
AT91C_PIOA_PDSR.byteEndian=little
AT91C_PIOA_PDSR.permission.write=none
AT91C_PIOA_PPUER.name="AT91C_PIOA_PPUER"
AT91C_PIOA_PPUER.description="Pull-up Enable Register"
AT91C_PIOA_PPUER.helpkey="Pull-up Enable Register"
AT91C_PIOA_PPUER.access=memorymapped
AT91C_PIOA_PPUER.address=0xFFFFF464
AT91C_PIOA_PPUER.width=32
AT91C_PIOA_PPUER.byteEndian=little
AT91C_PIOA_PPUER.type=enum
AT91C_PIOA_PPUER.enum.0.name=*** Write only ***
AT91C_PIOA_PPUER.enum.1.name=Error
AT91C_PIOA_OSR.name="AT91C_PIOA_OSR"
AT91C_PIOA_OSR.description="Output Status Register"
AT91C_PIOA_OSR.helpkey="Output Status Register"
AT91C_PIOA_OSR.access=memorymapped
AT91C_PIOA_OSR.address=0xFFFFF418
AT91C_PIOA_OSR.width=32
AT91C_PIOA_OSR.byteEndian=little
AT91C_PIOA_OSR.permission.write=none
AT91C_PIOA_ASR.name="AT91C_PIOA_ASR"
AT91C_PIOA_ASR.description="Select A Register"
AT91C_PIOA_ASR.helpkey="Select A Register"
AT91C_PIOA_ASR.access=memorymapped
AT91C_PIOA_ASR.address=0xFFFFF470
AT91C_PIOA_ASR.width=32
AT91C_PIOA_ASR.byteEndian=little
AT91C_PIOA_ASR.type=enum
AT91C_PIOA_ASR.enum.0.name=*** Write only ***
AT91C_PIOA_ASR.enum.1.name=Error
AT91C_PIOA_MDDR.name="AT91C_PIOA_MDDR"
AT91C_PIOA_MDDR.description="Multi-driver Disable Register"
AT91C_PIOA_MDDR.helpkey="Multi-driver Disable Register"
AT91C_PIOA_MDDR.access=memorymapped
AT91C_PIOA_MDDR.address=0xFFFFF454
AT91C_PIOA_MDDR.width=32
AT91C_PIOA_MDDR.byteEndian=little
AT91C_PIOA_MDDR.type=enum
AT91C_PIOA_MDDR.enum.0.name=*** Write only ***
AT91C_PIOA_MDDR.enum.1.name=Error
AT91C_PIOA_CODR.name="AT91C_PIOA_CODR"
AT91C_PIOA_CODR.description="Clear Output Data Register"
AT91C_PIOA_CODR.helpkey="Clear Output Data Register"
AT91C_PIOA_CODR.access=memorymapped
AT91C_PIOA_CODR.address=0xFFFFF434
AT91C_PIOA_CODR.width=32
AT91C_PIOA_CODR.byteEndian=little
AT91C_PIOA_CODR.type=enum
AT91C_PIOA_CODR.enum.0.name=*** Write only ***
AT91C_PIOA_CODR.enum.1.name=Error
AT91C_PIOA_MDER.name="AT91C_PIOA_MDER"
AT91C_PIOA_MDER.description="Multi-driver Enable Register"
AT91C_PIOA_MDER.helpkey="Multi-driver Enable Register"
AT91C_PIOA_MDER.access=memorymapped
AT91C_PIOA_MDER.address=0xFFFFF450
AT91C_PIOA_MDER.width=32
AT91C_PIOA_MDER.byteEndian=little
AT91C_PIOA_MDER.type=enum
AT91C_PIOA_MDER.enum.0.name=*** Write only ***
AT91C_PIOA_MDER.enum.1.name=Error
AT91C_PIOA_PDR.name="AT91C_PIOA_PDR"
AT91C_PIOA_PDR.description="PIO Disable Register"
AT91C_PIOA_PDR.helpkey="PIO Disable Register"
AT91C_PIOA_PDR.access=memorymapped
AT91C_PIOA_PDR.address=0xFFFFF404
AT91C_PIOA_PDR.width=32
AT91C_PIOA_PDR.byteEndian=little
AT91C_PIOA_PDR.type=enum
AT91C_PIOA_PDR.enum.0.name=*** Write only ***
AT91C_PIOA_PDR.enum.1.name=Error
AT91C_PIOA_IFSR.name="AT91C_PIOA_IFSR"
AT91C_PIOA_IFSR.description="Input Filter Status Register"
AT91C_PIOA_IFSR.helpkey="Input Filter Status Register"
AT91C_PIOA_IFSR.access=memorymapped
AT91C_PIOA_IFSR.address=0xFFFFF428
AT91C_PIOA_IFSR.width=32
AT91C_PIOA_IFSR.byteEndian=little
AT91C_PIOA_IFSR.permission.write=none
AT91C_PIOA_OER.name="AT91C_PIOA_OER"
AT91C_PIOA_OER.description="Output Enable Register"
AT91C_PIOA_OER.helpkey="Output Enable Register"
AT91C_PIOA_OER.access=memorymapped
AT91C_PIOA_OER.address=0xFFFFF410
AT91C_PIOA_OER.width=32
AT91C_PIOA_OER.byteEndian=little
AT91C_PIOA_OER.type=enum
AT91C_PIOA_OER.enum.0.name=*** Write only ***
AT91C_PIOA_OER.enum.1.name=Error
AT91C_PIOA_PSR.name="AT91C_PIOA_PSR"
AT91C_PIOA_PSR.description="PIO Status Register"
AT91C_PIOA_PSR.helpkey="PIO Status Register"
AT91C_PIOA_PSR.access=memorymapped
AT91C_PIOA_PSR.address=0xFFFFF408
AT91C_PIOA_PSR.width=32
AT91C_PIOA_PSR.byteEndian=little
AT91C_PIOA_PSR.permission.write=none
# ========== Register definition for PIOB peripheral ========== 
AT91C_PIOB_OWDR.name="AT91C_PIOB_OWDR"
AT91C_PIOB_OWDR.description="Output Write Disable Register"
AT91C_PIOB_OWDR.helpkey="Output Write Disable Register"
AT91C_PIOB_OWDR.access=memorymapped
AT91C_PIOB_OWDR.address=0xFFFFF6A4
AT91C_PIOB_OWDR.width=32
AT91C_PIOB_OWDR.byteEndian=little
AT91C_PIOB_OWDR.type=enum
AT91C_PIOB_OWDR.enum.0.name=*** Write only ***
AT91C_PIOB_OWDR.enum.1.name=Error
AT91C_PIOB_MDER.name="AT91C_PIOB_MDER"
AT91C_PIOB_MDER.description="Multi-driver Enable Register"
AT91C_PIOB_MDER.helpkey="Multi-driver Enable Register"
AT91C_PIOB_MDER.access=memorymapped
AT91C_PIOB_MDER.address=0xFFFFF650
AT91C_PIOB_MDER.width=32
AT91C_PIOB_MDER.byteEndian=little
AT91C_PIOB_MDER.type=enum
AT91C_PIOB_MDER.enum.0.name=*** Write only ***
AT91C_PIOB_MDER.enum.1.name=Error
AT91C_PIOB_PPUSR.name="AT91C_PIOB_PPUSR"
AT91C_PIOB_PPUSR.description="Pull-up Status Register"
AT91C_PIOB_PPUSR.helpkey="Pull-up Status Register"
AT91C_PIOB_PPUSR.access=memorymapped
AT91C_PIOB_PPUSR.address=0xFFFFF668
AT91C_PIOB_PPUSR.width=32
AT91C_PIOB_PPUSR.byteEndian=little
AT91C_PIOB_PPUSR.permission.write=none
AT91C_PIOB_IMR.name="AT91C_PIOB_IMR"
AT91C_PIOB_IMR.description="Interrupt Mask Register"
AT91C_PIOB_IMR.helpkey="Interrupt Mask Register"
AT91C_PIOB_IMR.access=memorymapped
AT91C_PIOB_IMR.address=0xFFFFF648
AT91C_PIOB_IMR.width=32
AT91C_PIOB_IMR.byteEndian=little
AT91C_PIOB_IMR.permission.write=none
AT91C_PIOB_ASR.name="AT91C_PIOB_ASR"
AT91C_PIOB_ASR.description="Select A Register"
AT91C_PIOB_ASR.helpkey="Select A Register"
AT91C_PIOB_ASR.access=memorymapped
AT91C_PIOB_ASR.address=0xFFFFF670
AT91C_PIOB_ASR.width=32
AT91C_PIOB_ASR.byteEndian=little
AT91C_PIOB_ASR.type=enum
AT91C_PIOB_ASR.enum.0.name=*** Write only ***
AT91C_PIOB_ASR.enum.1.name=Error
AT91C_PIOB_PPUDR.name="AT91C_PIOB_PPUDR"
AT91C_PIOB_PPUDR.description="Pull-up Disable Register"
AT91C_PIOB_PPUDR.helpkey="Pull-up Disable Register"
AT91C_PIOB_PPUDR.access=memorymapped
AT91C_PIOB_PPUDR.address=0xFFFFF660
AT91C_PIOB_PPUDR.width=32
AT91C_PIOB_PPUDR.byteEndian=little
AT91C_PIOB_PPUDR.type=enum
AT91C_PIOB_PPUDR.enum.0.name=*** Write only ***
AT91C_PIOB_PPUDR.enum.1.name=Error
AT91C_PIOB_PSR.name="AT91C_PIOB_PSR"
AT91C_PIOB_PSR.description="PIO Status Register"
AT91C_PIOB_PSR.helpkey="PIO Status Register"
AT91C_PIOB_PSR.access=memorymapped
AT91C_PIOB_PSR.address=0xFFFFF608
AT91C_PIOB_PSR.width=32
AT91C_PIOB_PSR.byteEndian=little
AT91C_PIOB_PSR.permission.write=none
AT91C_PIOB_IER.name="AT91C_PIOB_IER"
AT91C_PIOB_IER.description="Interrupt Enable Register"
AT91C_PIOB_IER.helpkey="Interrupt Enable Register"
AT91C_PIOB_IER.access=memorymapped
AT91C_PIOB_IER.address=0xFFFFF640
AT91C_PIOB_IER.width=32
AT91C_PIOB_IER.byteEndian=little
AT91C_PIOB_IER.type=enum
AT91C_PIOB_IER.enum.0.name=*** Write only ***
AT91C_PIOB_IER.enum.1.name=Error
AT91C_PIOB_CODR.name="AT91C_PIOB_CODR"
AT91C_PIOB_CODR.description="Clear Output Data Register"
AT91C_PIOB_CODR.helpkey="Clear Output Data Register"
AT91C_PIOB_CODR.access=memorymapped
AT91C_PIOB_CODR.address=0xFFFFF634
AT91C_PIOB_CODR.width=32
AT91C_PIOB_CODR.byteEndian=little
AT91C_PIOB_CODR.type=enum
AT91C_PIOB_CODR.enum.0.name=*** Write only ***
AT91C_PIOB_CODR.enum.1.name=Error
AT91C_PIOB_OWER.name="AT91C_PIOB_OWER"
AT91C_PIOB_OWER.description="Output Write Enable Register"
AT91C_PIOB_OWER.helpkey="Output Write Enable Register"
AT91C_PIOB_OWER.access=memorymapped
AT91C_PIOB_OWER.address=0xFFFFF6A0
AT91C_PIOB_OWER.width=32
AT91C_PIOB_OWER.byteEndian=little
AT91C_PIOB_OWER.type=enum
AT91C_PIOB_OWER.enum.0.name=*** Write only ***
AT91C_PIOB_OWER.enum.1.name=Error
AT91C_PIOB_ABSR.name="AT91C_PIOB_ABSR"
AT91C_PIOB_ABSR.description="AB Select Status Register"
AT91C_PIOB_ABSR.helpkey="AB Select Status Register"
AT91C_PIOB_ABSR.access=memorymapped
AT91C_PIOB_ABSR.address=0xFFFFF678
AT91C_PIOB_ABSR.width=32
AT91C_PIOB_ABSR.byteEndian=little
AT91C_PIOB_ABSR.permission.write=none
AT91C_PIOB_IFDR.name="AT91C_PIOB_IFDR"
AT91C_PIOB_IFDR.description="Input Filter Disable Register"
AT91C_PIOB_IFDR.helpkey="Input Filter Disable Register"
AT91C_PIOB_IFDR.access=memorymapped
AT91C_PIOB_IFDR.address=0xFFFFF624
AT91C_PIOB_IFDR.width=32
AT91C_PIOB_IFDR.byteEndian=little
AT91C_PIOB_IFDR.type=enum
AT91C_PIOB_IFDR.enum.0.name=*** Write only ***
AT91C_PIOB_IFDR.enum.1.name=Error
AT91C_PIOB_PDSR.name="AT91C_PIOB_PDSR"
AT91C_PIOB_PDSR.description="Pin Data Status Register"
AT91C_PIOB_PDSR.helpkey="Pin Data Status Register"
AT91C_PIOB_PDSR.access=memorymapped
AT91C_PIOB_PDSR.address=0xFFFFF63C
AT91C_PIOB_PDSR.width=32
AT91C_PIOB_PDSR.byteEndian=little
AT91C_PIOB_PDSR.permission.write=none
AT91C_PIOB_IDR.name="AT91C_PIOB_IDR"
AT91C_PIOB_IDR.description="Interrupt Disable Register"
AT91C_PIOB_IDR.helpkey="Interrupt Disable Register"
AT91C_PIOB_IDR.access=memorymapped
AT91C_PIOB_IDR.address=0xFFFFF644
AT91C_PIOB_IDR.width=32
AT91C_PIOB_IDR.byteEndian=little
AT91C_PIOB_IDR.type=enum
AT91C_PIOB_IDR.enum.0.name=*** Write only ***
AT91C_PIOB_IDR.enum.1.name=Error
AT91C_PIOB_OWSR.name="AT91C_PIOB_OWSR"
AT91C_PIOB_OWSR.description="Output Write Status Register"
AT91C_PIOB_OWSR.helpkey="Output Write Status Register"
AT91C_PIOB_OWSR.access=memorymapped
AT91C_PIOB_OWSR.address=0xFFFFF6A8
AT91C_PIOB_OWSR.width=32
AT91C_PIOB_OWSR.byteEndian=little
AT91C_PIOB_OWSR.permission.write=none
AT91C_PIOB_PDR.name="AT91C_PIOB_PDR"
AT91C_PIOB_PDR.description="PIO Disable Register"
AT91C_PIOB_PDR.helpkey="PIO Disable Register"
AT91C_PIOB_PDR.access=memorymapped
AT91C_PIOB_PDR.address=0xFFFFF604
AT91C_PIOB_PDR.width=32
AT91C_PIOB_PDR.byteEndian=little
AT91C_PIOB_PDR.type=enum
AT91C_PIOB_PDR.enum.0.name=*** Write only ***
AT91C_PIOB_PDR.enum.1.name=Error
AT91C_PIOB_ODR.name="AT91C_PIOB_ODR"
AT91C_PIOB_ODR.description="Output Disable Registerr"
AT91C_PIOB_ODR.helpkey="Output Disable Registerr"
AT91C_PIOB_ODR.access=memorymapped
AT91C_PIOB_ODR.address=0xFFFFF614
AT91C_PIOB_ODR.width=32
AT91C_PIOB_ODR.byteEndian=little
AT91C_PIOB_ODR.type=enum
AT91C_PIOB_ODR.enum.0.name=*** Write only ***
AT91C_PIOB_ODR.enum.1.name=Error
AT91C_PIOB_IFSR.name="AT91C_PIOB_IFSR"
AT91C_PIOB_IFSR.description="Input Filter Status Register"
AT91C_PIOB_IFSR.helpkey="Input Filter Status Register"
AT91C_PIOB_IFSR.access=memorymapped
AT91C_PIOB_IFSR.address=0xFFFFF628
AT91C_PIOB_IFSR.width=32
AT91C_PIOB_IFSR.byteEndian=little
AT91C_PIOB_IFSR.permission.write=none
AT91C_PIOB_PPUER.name="AT91C_PIOB_PPUER"
AT91C_PIOB_PPUER.description="Pull-up Enable Register"
AT91C_PIOB_PPUER.helpkey="Pull-up Enable Register"
AT91C_PIOB_PPUER.access=memorymapped
AT91C_PIOB_PPUER.address=0xFFFFF664
AT91C_PIOB_PPUER.width=32
AT91C_PIOB_PPUER.byteEndian=little
AT91C_PIOB_PPUER.type=enum
AT91C_PIOB_PPUER.enum.0.name=*** Write only ***
AT91C_PIOB_PPUER.enum.1.name=Error
AT91C_PIOB_SODR.name="AT91C_PIOB_SODR"
AT91C_PIOB_SODR.description="Set Output Data Register"
AT91C_PIOB_SODR.helpkey="Set Output Data Register"
AT91C_PIOB_SODR.access=memorymapped
AT91C_PIOB_SODR.address=0xFFFFF630
AT91C_PIOB_SODR.width=32
AT91C_PIOB_SODR.byteEndian=little
AT91C_PIOB_SODR.type=enum
AT91C_PIOB_SODR.enum.0.name=*** Write only ***
AT91C_PIOB_SODR.enum.1.name=Error
AT91C_PIOB_ISR.name="AT91C_PIOB_ISR"
AT91C_PIOB_ISR.description="Interrupt Status Register"
AT91C_PIOB_ISR.helpkey="Interrupt Status Register"
AT91C_PIOB_ISR.access=memorymapped
AT91C_PIOB_ISR.address=0xFFFFF64C
AT91C_PIOB_ISR.width=32
AT91C_PIOB_ISR.byteEndian=little
AT91C_PIOB_ISR.permission.write=none
AT91C_PIOB_ODSR.name="AT91C_PIOB_ODSR"
AT91C_PIOB_ODSR.description="Output Data Status Register"
AT91C_PIOB_ODSR.helpkey="Output Data Status Register"
AT91C_PIOB_ODSR.access=memorymapped
AT91C_PIOB_ODSR.address=0xFFFFF638
AT91C_PIOB_ODSR.width=32
AT91C_PIOB_ODSR.byteEndian=little
AT91C_PIOB_ODSR.permission.write=none
AT91C_PIOB_OSR.name="AT91C_PIOB_OSR"
AT91C_PIOB_OSR.description="Output Status Register"
AT91C_PIOB_OSR.helpkey="Output Status Register"
AT91C_PIOB_OSR.access=memorymapped
AT91C_PIOB_OSR.address=0xFFFFF618
AT91C_PIOB_OSR.width=32
AT91C_PIOB_OSR.byteEndian=little
AT91C_PIOB_OSR.permission.write=none
AT91C_PIOB_MDSR.name="AT91C_PIOB_MDSR"
AT91C_PIOB_MDSR.description="Multi-driver Status Register"
AT91C_PIOB_MDSR.helpkey="Multi-driver Status Register"
AT91C_PIOB_MDSR.access=memorymapped
AT91C_PIOB_MDSR.address=0xFFFFF658
AT91C_PIOB_MDSR.width=32
AT91C_PIOB_MDSR.byteEndian=little
AT91C_PIOB_MDSR.permission.write=none
AT91C_PIOB_IFER.name="AT91C_PIOB_IFER"
AT91C_PIOB_IFER.description="Input Filter Enable Register"
AT91C_PIOB_IFER.helpkey="Input Filter Enable Register"
AT91C_PIOB_IFER.access=memorymapped
AT91C_PIOB_IFER.address=0xFFFFF620
AT91C_PIOB_IFER.width=32
AT91C_PIOB_IFER.byteEndian=little
AT91C_PIOB_IFER.type=enum
AT91C_PIOB_IFER.enum.0.name=*** Write only ***
AT91C_PIOB_IFER.enum.1.name=Error
AT91C_PIOB_BSR.name="AT91C_PIOB_BSR"
AT91C_PIOB_BSR.description="Select B Register"
AT91C_PIOB_BSR.helpkey="Select B Register"
AT91C_PIOB_BSR.access=memorymapped
AT91C_PIOB_BSR.address=0xFFFFF674
AT91C_PIOB_BSR.width=32
AT91C_PIOB_BSR.byteEndian=little
AT91C_PIOB_BSR.type=enum
AT91C_PIOB_BSR.enum.0.name=*** Write only ***
AT91C_PIOB_BSR.enum.1.name=Error
AT91C_PIOB_MDDR.name="AT91C_PIOB_MDDR"
AT91C_PIOB_MDDR.description="Multi-driver Disable Register"
AT91C_PIOB_MDDR.helpkey="Multi-driver Disable Register"
AT91C_PIOB_MDDR.access=memorymapped
AT91C_PIOB_MDDR.address=0xFFFFF654
AT91C_PIOB_MDDR.width=32
AT91C_PIOB_MDDR.byteEndian=little
AT91C_PIOB_MDDR.type=enum
AT91C_PIOB_MDDR.enum.0.name=*** Write only ***
AT91C_PIOB_MDDR.enum.1.name=Error
AT91C_PIOB_OER.name="AT91C_PIOB_OER"
AT91C_PIOB_OER.description="Output Enable Register"
AT91C_PIOB_OER.helpkey="Output Enable Register"
AT91C_PIOB_OER.access=memorymapped
AT91C_PIOB_OER.address=0xFFFFF610
AT91C_PIOB_OER.width=32
AT91C_PIOB_OER.byteEndian=little
AT91C_PIOB_OER.type=enum
AT91C_PIOB_OER.enum.0.name=*** Write only ***
AT91C_PIOB_OER.enum.1.name=Error
AT91C_PIOB_PER.name="AT91C_PIOB_PER"
AT91C_PIOB_PER.description="PIO Enable Register"
AT91C_PIOB_PER.helpkey="PIO Enable Register"
AT91C_PIOB_PER.access=memorymapped
AT91C_PIOB_PER.address=0xFFFFF600
AT91C_PIOB_PER.width=32
AT91C_PIOB_PER.byteEndian=little
AT91C_PIOB_PER.type=enum
AT91C_PIOB_PER.enum.0.name=*** Write only ***
AT91C_PIOB_PER.enum.1.name=Error
# ========== Register definition for CKGR peripheral ========== 
AT91C_CKGR_MOR.name="AT91C_CKGR_MOR"
AT91C_CKGR_MOR.description="Main Oscillator Register"
AT91C_CKGR_MOR.helpkey="Main Oscillator Register"
AT91C_CKGR_MOR.access=memorymapped
AT91C_CKGR_MOR.address=0xFFFFFC20
AT91C_CKGR_MOR.width=32
AT91C_CKGR_MOR.byteEndian=little
AT91C_CKGR_PLLR.name="AT91C_CKGR_PLLR"
AT91C_CKGR_PLLR.description="PLL Register"
AT91C_CKGR_PLLR.helpkey="PLL Register"
AT91C_CKGR_PLLR.access=memorymapped
AT91C_CKGR_PLLR.address=0xFFFFFC2C
AT91C_CKGR_PLLR.width=32
AT91C_CKGR_PLLR.byteEndian=little
AT91C_CKGR_MCFR.name="AT91C_CKGR_MCFR"
AT91C_CKGR_MCFR.description="Main Clock  Frequency Register"
AT91C_CKGR_MCFR.helpkey="Main Clock  Frequency Register"
AT91C_CKGR_MCFR.access=memorymapped
AT91C_CKGR_MCFR.address=0xFFFFFC24
AT91C_CKGR_MCFR.width=32
AT91C_CKGR_MCFR.byteEndian=little
AT91C_CKGR_MCFR.permission.write=none
# ========== Register definition for PMC peripheral ========== 
AT91C_PMC_IDR.name="AT91C_PMC_IDR"
AT91C_PMC_IDR.description="Interrupt Disable Register"
AT91C_PMC_IDR.helpkey="Interrupt Disable Register"
AT91C_PMC_IDR.access=memorymapped
AT91C_PMC_IDR.address=0xFFFFFC64
AT91C_PMC_IDR.width=32
AT91C_PMC_IDR.byteEndian=little
AT91C_PMC_IDR.type=enum
AT91C_PMC_IDR.enum.0.name=*** Write only ***
AT91C_PMC_IDR.enum.1.name=Error
AT91C_PMC_MOR.name="AT91C_PMC_MOR"
AT91C_PMC_MOR.description="Main Oscillator Register"
AT91C_PMC_MOR.helpkey="Main Oscillator Register"
AT91C_PMC_MOR.access=memorymapped
AT91C_PMC_MOR.address=0xFFFFFC20
AT91C_PMC_MOR.width=32
AT91C_PMC_MOR.byteEndian=little
AT91C_PMC_PLLR.name="AT91C_PMC_PLLR"
AT91C_PMC_PLLR.description="PLL Register"
AT91C_PMC_PLLR.helpkey="PLL Register"
AT91C_PMC_PLLR.access=memorymapped
AT91C_PMC_PLLR.address=0xFFFFFC2C
AT91C_PMC_PLLR.width=32
AT91C_PMC_PLLR.byteEndian=little
AT91C_PMC_PCER.name="AT91C_PMC_PCER"
AT91C_PMC_PCER.description="Peripheral Clock Enable Register"
AT91C_PMC_PCER.helpkey="Peripheral Clock Enable Register"
AT91C_PMC_PCER.access=memorymapped
AT91C_PMC_PCER.address=0xFFFFFC10
AT91C_PMC_PCER.width=32
AT91C_PMC_PCER.byteEndian=little
AT91C_PMC_PCER.type=enum
AT91C_PMC_PCER.enum.0.name=*** Write only ***
AT91C_PMC_PCER.enum.1.name=Error
AT91C_PMC_PCKR.name="AT91C_PMC_PCKR"
AT91C_PMC_PCKR.description="Programmable Clock Register"
AT91C_PMC_PCKR.helpkey="Programmable Clock Register"
AT91C_PMC_PCKR.access=memorymapped
AT91C_PMC_PCKR.address=0xFFFFFC40
AT91C_PMC_PCKR.width=32
AT91C_PMC_PCKR.byteEndian=little
AT91C_PMC_MCKR.name="AT91C_PMC_MCKR"
AT91C_PMC_MCKR.description="Master Clock Register"
AT91C_PMC_MCKR.helpkey="Master Clock Register"
AT91C_PMC_MCKR.access=memorymapped
AT91C_PMC_MCKR.address=0xFFFFFC30
AT91C_PMC_MCKR.width=32
AT91C_PMC_MCKR.byteEndian=little
AT91C_PMC_SCDR.name="AT91C_PMC_SCDR"
AT91C_PMC_SCDR.description="System Clock Disable Register"
AT91C_PMC_SCDR.helpkey="System Clock Disable Register"
AT91C_PMC_SCDR.access=memorymapped
AT91C_PMC_SCDR.address=0xFFFFFC04
AT91C_PMC_SCDR.width=32
AT91C_PMC_SCDR.byteEndian=little
AT91C_PMC_SCDR.type=enum
AT91C_PMC_SCDR.enum.0.name=*** Write only ***
AT91C_PMC_SCDR.enum.1.name=Error
AT91C_PMC_PCDR.name="AT91C_PMC_PCDR"
AT91C_PMC_PCDR.description="Peripheral Clock Disable Register"
AT91C_PMC_PCDR.helpkey="Peripheral Clock Disable Register"
AT91C_PMC_PCDR.access=memorymapped
AT91C_PMC_PCDR.address=0xFFFFFC14
AT91C_PMC_PCDR.width=32
AT91C_PMC_PCDR.byteEndian=little
AT91C_PMC_PCDR.type=enum
AT91C_PMC_PCDR.enum.0.name=*** Write only ***
AT91C_PMC_PCDR.enum.1.name=Error
AT91C_PMC_SCSR.name="AT91C_PMC_SCSR"
AT91C_PMC_SCSR.description="System Clock Status Register"
AT91C_PMC_SCSR.helpkey="System Clock Status Register"
AT91C_PMC_SCSR.access=memorymapped
AT91C_PMC_SCSR.address=0xFFFFFC08
AT91C_PMC_SCSR.width=32
AT91C_PMC_SCSR.byteEndian=little
AT91C_PMC_SCSR.permission.write=none
AT91C_PMC_PCSR.name="AT91C_PMC_PCSR"
AT91C_PMC_PCSR.description="Peripheral Clock Status Register"
AT91C_PMC_PCSR.helpkey="Peripheral Clock Status Register"
AT91C_PMC_PCSR.access=memorymapped
AT91C_PMC_PCSR.address=0xFFFFFC18
AT91C_PMC_PCSR.width=32
AT91C_PMC_PCSR.byteEndian=little
AT91C_PMC_PCSR.permission.write=none
AT91C_PMC_MCFR.name="AT91C_PMC_MCFR"
AT91C_PMC_MCFR.description="Main Clock  Frequency Register"
AT91C_PMC_MCFR.helpkey="Main Clock  Frequency Register"
AT91C_PMC_MCFR.access=memorymapped
AT91C_PMC_MCFR.address=0xFFFFFC24
AT91C_PMC_MCFR.width=32
AT91C_PMC_MCFR.byteEndian=little
AT91C_PMC_MCFR.permission.write=none
AT91C_PMC_SCER.name="AT91C_PMC_SCER"
AT91C_PMC_SCER.description="System Clock Enable Register"
AT91C_PMC_SCER.helpkey="System Clock Enable Register"
AT91C_PMC_SCER.access=memorymapped
AT91C_PMC_SCER.address=0xFFFFFC00
AT91C_PMC_SCER.width=32
AT91C_PMC_SCER.byteEndian=little
AT91C_PMC_SCER.type=enum
AT91C_PMC_SCER.enum.0.name=*** Write only ***
AT91C_PMC_SCER.enum.1.name=Error
AT91C_PMC_IMR.name="AT91C_PMC_IMR"
AT91C_PMC_IMR.description="Interrupt Mask Register"
AT91C_PMC_IMR.helpkey="Interrupt Mask Register"
AT91C_PMC_IMR.access=memorymapped
AT91C_PMC_IMR.address=0xFFFFFC6C
AT91C_PMC_IMR.width=32
AT91C_PMC_IMR.byteEndian=little
AT91C_PMC_IMR.permission.write=none
AT91C_PMC_IER.name="AT91C_PMC_IER"
AT91C_PMC_IER.description="Interrupt Enable Register"
AT91C_PMC_IER.helpkey="Interrupt Enable Register"
AT91C_PMC_IER.access=memorymapped
AT91C_PMC_IER.address=0xFFFFFC60
AT91C_PMC_IER.width=32
AT91C_PMC_IER.byteEndian=little
AT91C_PMC_IER.type=enum
AT91C_PMC_IER.enum.0.name=*** Write only ***
AT91C_PMC_IER.enum.1.name=Error
AT91C_PMC_SR.name="AT91C_PMC_SR"
AT91C_PMC_SR.description="Status Register"
AT91C_PMC_SR.helpkey="Status Register"
AT91C_PMC_SR.access=memorymapped
AT91C_PMC_SR.address=0xFFFFFC68
AT91C_PMC_SR.width=32
AT91C_PMC_SR.byteEndian=little
AT91C_PMC_SR.permission.write=none
# ========== Register definition for RSTC peripheral ========== 
AT91C_RSTC_RCR.name="AT91C_RSTC_RCR"
AT91C_RSTC_RCR.description="Reset Control Register"
AT91C_RSTC_RCR.helpkey="Reset Control Register"
AT91C_RSTC_RCR.access=memorymapped
AT91C_RSTC_RCR.address=0xFFFFFD00
AT91C_RSTC_RCR.width=32
AT91C_RSTC_RCR.byteEndian=little
AT91C_RSTC_RCR.type=enum
AT91C_RSTC_RCR.enum.0.name=*** Write only ***
AT91C_RSTC_RCR.enum.1.name=Error
AT91C_RSTC_RMR.name="AT91C_RSTC_RMR"
AT91C_RSTC_RMR.description="Reset Mode Register"
AT91C_RSTC_RMR.helpkey="Reset Mode Register"
AT91C_RSTC_RMR.access=memorymapped
AT91C_RSTC_RMR.address=0xFFFFFD08
AT91C_RSTC_RMR.width=32
AT91C_RSTC_RMR.byteEndian=little
AT91C_RSTC_RSR.name="AT91C_RSTC_RSR"
AT91C_RSTC_RSR.description="Reset Status Register"
AT91C_RSTC_RSR.helpkey="Reset Status Register"
AT91C_RSTC_RSR.access=memorymapped
AT91C_RSTC_RSR.address=0xFFFFFD04
AT91C_RSTC_RSR.width=32
AT91C_RSTC_RSR.byteEndian=little
AT91C_RSTC_RSR.permission.write=none
# ========== Register definition for RTTC peripheral ========== 
AT91C_RTTC_RTSR.name="AT91C_RTTC_RTSR"
AT91C_RTTC_RTSR.description="Real-time Status Register"
AT91C_RTTC_RTSR.helpkey="Real-time Status Register"
AT91C_RTTC_RTSR.access=memorymapped
AT91C_RTTC_RTSR.address=0xFFFFFD2C
AT91C_RTTC_RTSR.width=32
AT91C_RTTC_RTSR.byteEndian=little
AT91C_RTTC_RTSR.permission.write=none
AT91C_RTTC_RTMR.name="AT91C_RTTC_RTMR"
AT91C_RTTC_RTMR.description="Real-time Mode Register"
AT91C_RTTC_RTMR.helpkey="Real-time Mode Register"
AT91C_RTTC_RTMR.access=memorymapped
AT91C_RTTC_RTMR.address=0xFFFFFD20
AT91C_RTTC_RTMR.width=32
AT91C_RTTC_RTMR.byteEndian=little
AT91C_RTTC_RTVR.name="AT91C_RTTC_RTVR"
AT91C_RTTC_RTVR.description="Real-time Value Register"
AT91C_RTTC_RTVR.helpkey="Real-time Value Register"
AT91C_RTTC_RTVR.access=memorymapped
AT91C_RTTC_RTVR.address=0xFFFFFD28
AT91C_RTTC_RTVR.width=32
AT91C_RTTC_RTVR.byteEndian=little
AT91C_RTTC_RTVR.permission.write=none
AT91C_RTTC_RTAR.name="AT91C_RTTC_RTAR"
AT91C_RTTC_RTAR.description="Real-time Alarm Register"
AT91C_RTTC_RTAR.helpkey="Real-time Alarm Register"
AT91C_RTTC_RTAR.access=memorymapped
AT91C_RTTC_RTAR.address=0xFFFFFD24
AT91C_RTTC_RTAR.width=32
AT91C_RTTC_RTAR.byteEndian=little
# ========== Register definition for PITC peripheral ========== 
AT91C_PITC_PIVR.name="AT91C_PITC_PIVR"
AT91C_PITC_PIVR.description="Period Interval Value Register"
AT91C_PITC_PIVR.helpkey="Period Interval Value Register"
AT91C_PITC_PIVR.access=memorymapped
AT91C_PITC_PIVR.address=0xFFFFFD38
AT91C_PITC_PIVR.width=32
AT91C_PITC_PIVR.byteEndian=little
AT91C_PITC_PIVR.permission.write=none
AT91C_PITC_PISR.name="AT91C_PITC_PISR"
AT91C_PITC_PISR.description="Period Interval Status Register"
AT91C_PITC_PISR.helpkey="Period Interval Status Register"
AT91C_PITC_PISR.access=memorymapped
AT91C_PITC_PISR.address=0xFFFFFD34
AT91C_PITC_PISR.width=32
AT91C_PITC_PISR.byteEndian=little
AT91C_PITC_PISR.permission.write=none
AT91C_PITC_PIIR.name="AT91C_PITC_PIIR"
AT91C_PITC_PIIR.description="Period Interval Image Register"
AT91C_PITC_PIIR.helpkey="Period Interval Image Register"
AT91C_PITC_PIIR.access=memorymapped
AT91C_PITC_PIIR.address=0xFFFFFD3C
AT91C_PITC_PIIR.width=32
AT91C_PITC_PIIR.byteEndian=little
AT91C_PITC_PIIR.permission.write=none
AT91C_PITC_PIMR.name="AT91C_PITC_PIMR"
AT91C_PITC_PIMR.description="Period Interval Mode Register"
AT91C_PITC_PIMR.helpkey="Period Interval Mode Register"
AT91C_PITC_PIMR.access=memorymapped
AT91C_PITC_PIMR.address=0xFFFFFD30
AT91C_PITC_PIMR.width=32
AT91C_PITC_PIMR.byteEndian=little
# ========== Register definition for WDTC peripheral ========== 
AT91C_WDTC_WDCR.name="AT91C_WDTC_WDCR"
AT91C_WDTC_WDCR.description="Watchdog Control Register"
AT91C_WDTC_WDCR.helpkey="Watchdog Control Register"
AT91C_WDTC_WDCR.access=memorymapped
AT91C_WDTC_WDCR.address=0xFFFFFD40
AT91C_WDTC_WDCR.width=32
AT91C_WDTC_WDCR.byteEndian=little
AT91C_WDTC_WDCR.type=enum
AT91C_WDTC_WDCR.enum.0.name=*** Write only ***
AT91C_WDTC_WDCR.enum.1.name=Error
AT91C_WDTC_WDSR.name="AT91C_WDTC_WDSR"
AT91C_WDTC_WDSR.description="Watchdog Status Register"
AT91C_WDTC_WDSR.helpkey="Watchdog Status Register"
AT91C_WDTC_WDSR.access=memorymapped
AT91C_WDTC_WDSR.address=0xFFFFFD48
AT91C_WDTC_WDSR.width=32
AT91C_WDTC_WDSR.byteEndian=little
AT91C_WDTC_WDSR.permission.write=none
AT91C_WDTC_WDMR.name="AT91C_WDTC_WDMR"
AT91C_WDTC_WDMR.description="Watchdog Mode Register"
AT91C_WDTC_WDMR.helpkey="Watchdog Mode Register"
AT91C_WDTC_WDMR.access=memorymapped
AT91C_WDTC_WDMR.address=0xFFFFFD44
AT91C_WDTC_WDMR.width=32
AT91C_WDTC_WDMR.byteEndian=little
# ========== Register definition for VREG peripheral ========== 
AT91C_VREG_MR.name="AT91C_VREG_MR"
AT91C_VREG_MR.description="Voltage Regulator Mode Register"
AT91C_VREG_MR.helpkey="Voltage Regulator Mode Register"
AT91C_VREG_MR.access=memorymapped
AT91C_VREG_MR.address=0xFFFFFD60
AT91C_VREG_MR.width=32
AT91C_VREG_MR.byteEndian=little
# ========== Register definition for MC peripheral ========== 
AT91C_MC_ASR.name="AT91C_MC_ASR"
AT91C_MC_ASR.description="MC Abort Status Register"
AT91C_MC_ASR.helpkey="MC Abort Status Register"
AT91C_MC_ASR.access=memorymapped
AT91C_MC_ASR.address=0xFFFFFF04
AT91C_MC_ASR.width=32
AT91C_MC_ASR.byteEndian=little
AT91C_MC_ASR.permission.write=none
AT91C_MC_RCR.name="AT91C_MC_RCR"
AT91C_MC_RCR.description="MC Remap Control Register"
AT91C_MC_RCR.helpkey="MC Remap Control Register"
AT91C_MC_RCR.access=memorymapped
AT91C_MC_RCR.address=0xFFFFFF00
AT91C_MC_RCR.width=32
AT91C_MC_RCR.byteEndian=little
AT91C_MC_RCR.type=enum
AT91C_MC_RCR.enum.0.name=*** Write only ***
AT91C_MC_RCR.enum.1.name=Error
AT91C_MC_FCR.name="AT91C_MC_FCR"
AT91C_MC_FCR.description="MC Flash Command Register"
AT91C_MC_FCR.helpkey="MC Flash Command Register"
AT91C_MC_FCR.access=memorymapped
AT91C_MC_FCR.address=0xFFFFFF64
AT91C_MC_FCR.width=32
AT91C_MC_FCR.byteEndian=little
AT91C_MC_FCR.type=enum
AT91C_MC_FCR.enum.0.name=*** Write only ***
AT91C_MC_FCR.enum.1.name=Error
AT91C_MC_AASR.name="AT91C_MC_AASR"
AT91C_MC_AASR.description="MC Abort Address Status Register"
AT91C_MC_AASR.helpkey="MC Abort Address Status Register"
AT91C_MC_AASR.access=memorymapped
AT91C_MC_AASR.address=0xFFFFFF08
AT91C_MC_AASR.width=32
AT91C_MC_AASR.byteEndian=little
AT91C_MC_AASR.permission.write=none
AT91C_MC_FSR.name="AT91C_MC_FSR"
AT91C_MC_FSR.description="MC Flash Status Register"
AT91C_MC_FSR.helpkey="MC Flash Status Register"
AT91C_MC_FSR.access=memorymapped
AT91C_MC_FSR.address=0xFFFFFF68
AT91C_MC_FSR.width=32
AT91C_MC_FSR.byteEndian=little
AT91C_MC_FSR.permission.write=none
AT91C_MC_FMR.name="AT91C_MC_FMR"
AT91C_MC_FMR.description="MC Flash Mode Register"
AT91C_MC_FMR.helpkey="MC Flash Mode Register"
AT91C_MC_FMR.access=memorymapped
AT91C_MC_FMR.address=0xFFFFFF60
AT91C_MC_FMR.width=32
AT91C_MC_FMR.byteEndian=little
# ========== Register definition for PDC_SPI1 peripheral ========== 
AT91C_SPI1_PTCR.name="AT91C_SPI1_PTCR"
AT91C_SPI1_PTCR.description="PDC Transfer Control Register"
AT91C_SPI1_PTCR.helpkey="PDC Transfer Control Register"
AT91C_SPI1_PTCR.access=memorymapped
AT91C_SPI1_PTCR.address=0xFFFE4120
AT91C_SPI1_PTCR.width=32
AT91C_SPI1_PTCR.byteEndian=little
AT91C_SPI1_PTCR.type=enum
AT91C_SPI1_PTCR.enum.0.name=*** Write only ***
AT91C_SPI1_PTCR.enum.1.name=Error
AT91C_SPI1_RPR.name="AT91C_SPI1_RPR"
AT91C_SPI1_RPR.description="Receive Pointer Register"
AT91C_SPI1_RPR.helpkey="Receive Pointer Register"
AT91C_SPI1_RPR.access=memorymapped
AT91C_SPI1_RPR.address=0xFFFE4100
AT91C_SPI1_RPR.width=32
AT91C_SPI1_RPR.byteEndian=little
AT91C_SPI1_TNCR.name="AT91C_SPI1_TNCR"
AT91C_SPI1_TNCR.description="Transmit Next Counter Register"
AT91C_SPI1_TNCR.helpkey="Transmit Next Counter Register"
AT91C_SPI1_TNCR.access=memorymapped
AT91C_SPI1_TNCR.address=0xFFFE411C
AT91C_SPI1_TNCR.width=32
AT91C_SPI1_TNCR.byteEndian=little
AT91C_SPI1_TPR.name="AT91C_SPI1_TPR"
AT91C_SPI1_TPR.description="Transmit Pointer Register"
AT91C_SPI1_TPR.helpkey="Transmit Pointer Register"
AT91C_SPI1_TPR.access=memorymapped
AT91C_SPI1_TPR.address=0xFFFE4108
AT91C_SPI1_TPR.width=32
AT91C_SPI1_TPR.byteEndian=little
AT91C_SPI1_TNPR.name="AT91C_SPI1_TNPR"
AT91C_SPI1_TNPR.description="Transmit Next Pointer Register"
AT91C_SPI1_TNPR.helpkey="Transmit Next Pointer Register"
AT91C_SPI1_TNPR.access=memorymapped
AT91C_SPI1_TNPR.address=0xFFFE4118
AT91C_SPI1_TNPR.width=32
AT91C_SPI1_TNPR.byteEndian=little
AT91C_SPI1_TCR.name="AT91C_SPI1_TCR"
AT91C_SPI1_TCR.description="Transmit Counter Register"
AT91C_SPI1_TCR.helpkey="Transmit Counter Register"
AT91C_SPI1_TCR.access=memorymapped
AT91C_SPI1_TCR.address=0xFFFE410C
AT91C_SPI1_TCR.width=32
AT91C_SPI1_TCR.byteEndian=little
AT91C_SPI1_RCR.name="AT91C_SPI1_RCR"
AT91C_SPI1_RCR.description="Receive Counter Register"
AT91C_SPI1_RCR.helpkey="Receive Counter Register"
AT91C_SPI1_RCR.access=memorymapped
AT91C_SPI1_RCR.address=0xFFFE4104
AT91C_SPI1_RCR.width=32
AT91C_SPI1_RCR.byteEndian=little
AT91C_SPI1_RNPR.name="AT91C_SPI1_RNPR"
AT91C_SPI1_RNPR.description="Receive Next Pointer Register"
AT91C_SPI1_RNPR.helpkey="Receive Next Pointer Register"
AT91C_SPI1_RNPR.access=memorymapped
AT91C_SPI1_RNPR.address=0xFFFE4110
AT91C_SPI1_RNPR.width=32
AT91C_SPI1_RNPR.byteEndian=little
AT91C_SPI1_RNCR.name="AT91C_SPI1_RNCR"
AT91C_SPI1_RNCR.description="Receive Next Counter Register"
AT91C_SPI1_RNCR.helpkey="Receive Next Counter Register"
AT91C_SPI1_RNCR.access=memorymapped
AT91C_SPI1_RNCR.address=0xFFFE4114
AT91C_SPI1_RNCR.width=32
AT91C_SPI1_RNCR.byteEndian=little
AT91C_SPI1_PTSR.name="AT91C_SPI1_PTSR"
AT91C_SPI1_PTSR.description="PDC Transfer Status Register"
AT91C_SPI1_PTSR.helpkey="PDC Transfer Status Register"
AT91C_SPI1_PTSR.access=memorymapped
AT91C_SPI1_PTSR.address=0xFFFE4124
AT91C_SPI1_PTSR.width=32
AT91C_SPI1_PTSR.byteEndian=little
AT91C_SPI1_PTSR.permission.write=none
# ========== Register definition for SPI1 peripheral ========== 
AT91C_SPI1_IMR.name="AT91C_SPI1_IMR"
AT91C_SPI1_IMR.description="Interrupt Mask Register"
AT91C_SPI1_IMR.helpkey="Interrupt Mask Register"
AT91C_SPI1_IMR.access=memorymapped
AT91C_SPI1_IMR.address=0xFFFE401C
AT91C_SPI1_IMR.width=32
AT91C_SPI1_IMR.byteEndian=little
AT91C_SPI1_IMR.permission.write=none
AT91C_SPI1_IER.name="AT91C_SPI1_IER"
AT91C_SPI1_IER.description="Interrupt Enable Register"
AT91C_SPI1_IER.helpkey="Interrupt Enable Register"
AT91C_SPI1_IER.access=memorymapped
AT91C_SPI1_IER.address=0xFFFE4014
AT91C_SPI1_IER.width=32
AT91C_SPI1_IER.byteEndian=little
AT91C_SPI1_IER.type=enum
AT91C_SPI1_IER.enum.0.name=*** Write only ***
AT91C_SPI1_IER.enum.1.name=Error
AT91C_SPI1_MR.name="AT91C_SPI1_MR"
AT91C_SPI1_MR.description="Mode Register"
AT91C_SPI1_MR.helpkey="Mode Register"
AT91C_SPI1_MR.access=memorymapped
AT91C_SPI1_MR.address=0xFFFE4004
AT91C_SPI1_MR.width=32
AT91C_SPI1_MR.byteEndian=little
AT91C_SPI1_RDR.name="AT91C_SPI1_RDR"
AT91C_SPI1_RDR.description="Receive Data Register"
AT91C_SPI1_RDR.helpkey="Receive Data Register"
AT91C_SPI1_RDR.access=memorymapped
AT91C_SPI1_RDR.address=0xFFFE4008
AT91C_SPI1_RDR.width=32
AT91C_SPI1_RDR.byteEndian=little
AT91C_SPI1_RDR.permission.write=none
AT91C_SPI1_IDR.name="AT91C_SPI1_IDR"
AT91C_SPI1_IDR.description="Interrupt Disable Register"
AT91C_SPI1_IDR.helpkey="Interrupt Disable Register"
AT91C_SPI1_IDR.access=memorymapped
AT91C_SPI1_IDR.address=0xFFFE4018
AT91C_SPI1_IDR.width=32
AT91C_SPI1_IDR.byteEndian=little
AT91C_SPI1_IDR.type=enum
AT91C_SPI1_IDR.enum.0.name=*** Write only ***
AT91C_SPI1_IDR.enum.1.name=Error
AT91C_SPI1_SR.name="AT91C_SPI1_SR"
AT91C_SPI1_SR.description="Status Register"
AT91C_SPI1_SR.helpkey="Status Register"
AT91C_SPI1_SR.access=memorymapped
AT91C_SPI1_SR.address=0xFFFE4010
AT91C_SPI1_SR.width=32
AT91C_SPI1_SR.byteEndian=little
AT91C_SPI1_SR.permission.write=none
AT91C_SPI1_TDR.name="AT91C_SPI1_TDR"
AT91C_SPI1_TDR.description="Transmit Data Register"
AT91C_SPI1_TDR.helpkey="Transmit Data Register"
AT91C_SPI1_TDR.access=memorymapped
AT91C_SPI1_TDR.address=0xFFFE400C
AT91C_SPI1_TDR.width=32
AT91C_SPI1_TDR.byteEndian=little
AT91C_SPI1_TDR.type=enum
AT91C_SPI1_TDR.enum.0.name=*** Write only ***
AT91C_SPI1_TDR.enum.1.name=Error
AT91C_SPI1_CR.name="AT91C_SPI1_CR"
AT91C_SPI1_CR.description="Control Register"
AT91C_SPI1_CR.helpkey="Control Register"
AT91C_SPI1_CR.access=memorymapped
AT91C_SPI1_CR.address=0xFFFE4000
AT91C_SPI1_CR.width=32
AT91C_SPI1_CR.byteEndian=little
AT91C_SPI1_CR.permission.write=none
AT91C_SPI1_CSR.name="AT91C_SPI1_CSR"
AT91C_SPI1_CSR.description="Chip Select Register"
AT91C_SPI1_CSR.helpkey="Chip Select Register"
AT91C_SPI1_CSR.access=memorymapped
AT91C_SPI1_CSR.address=0xFFFE4030
AT91C_SPI1_CSR.width=32
AT91C_SPI1_CSR.byteEndian=little
# ========== Register definition for PDC_SPI0 peripheral ========== 
AT91C_SPI0_PTCR.name="AT91C_SPI0_PTCR"
AT91C_SPI0_PTCR.description="PDC Transfer Control Register"
AT91C_SPI0_PTCR.helpkey="PDC Transfer Control Register"
AT91C_SPI0_PTCR.access=memorymapped
AT91C_SPI0_PTCR.address=0xFFFE0120
AT91C_SPI0_PTCR.width=32
AT91C_SPI0_PTCR.byteEndian=little
AT91C_SPI0_PTCR.type=enum
AT91C_SPI0_PTCR.enum.0.name=*** Write only ***
AT91C_SPI0_PTCR.enum.1.name=Error
AT91C_SPI0_TPR.name="AT91C_SPI0_TPR"
AT91C_SPI0_TPR.description="Transmit Pointer Register"
AT91C_SPI0_TPR.helpkey="Transmit Pointer Register"
AT91C_SPI0_TPR.access=memorymapped
AT91C_SPI0_TPR.address=0xFFFE0108
AT91C_SPI0_TPR.width=32
AT91C_SPI0_TPR.byteEndian=little
AT91C_SPI0_TCR.name="AT91C_SPI0_TCR"
AT91C_SPI0_TCR.description="Transmit Counter Register"
AT91C_SPI0_TCR.helpkey="Transmit Counter Register"
AT91C_SPI0_TCR.access=memorymapped
AT91C_SPI0_TCR.address=0xFFFE010C
AT91C_SPI0_TCR.width=32
AT91C_SPI0_TCR.byteEndian=little
AT91C_SPI0_RCR.name="AT91C_SPI0_RCR"
AT91C_SPI0_RCR.description="Receive Counter Register"
AT91C_SPI0_RCR.helpkey="Receive Counter Register"
AT91C_SPI0_RCR.access=memorymapped
AT91C_SPI0_RCR.address=0xFFFE0104
AT91C_SPI0_RCR.width=32
AT91C_SPI0_RCR.byteEndian=little
AT91C_SPI0_PTSR.name="AT91C_SPI0_PTSR"
AT91C_SPI0_PTSR.description="PDC Transfer Status Register"
AT91C_SPI0_PTSR.helpkey="PDC Transfer Status Register"
AT91C_SPI0_PTSR.access=memorymapped
AT91C_SPI0_PTSR.address=0xFFFE0124
AT91C_SPI0_PTSR.width=32
AT91C_SPI0_PTSR.byteEndian=little
AT91C_SPI0_PTSR.permission.write=none
AT91C_SPI0_RNPR.name="AT91C_SPI0_RNPR"
AT91C_SPI0_RNPR.description="Receive Next Pointer Register"
AT91C_SPI0_RNPR.helpkey="Receive Next Pointer Register"
AT91C_SPI0_RNPR.access=memorymapped
AT91C_SPI0_RNPR.address=0xFFFE0110
AT91C_SPI0_RNPR.width=32
AT91C_SPI0_RNPR.byteEndian=little
AT91C_SPI0_RPR.name="AT91C_SPI0_RPR"
AT91C_SPI0_RPR.description="Receive Pointer Register"
AT91C_SPI0_RPR.helpkey="Receive Pointer Register"
AT91C_SPI0_RPR.access=memorymapped
AT91C_SPI0_RPR.address=0xFFFE0100
AT91C_SPI0_RPR.width=32
AT91C_SPI0_RPR.byteEndian=little
AT91C_SPI0_TNCR.name="AT91C_SPI0_TNCR"
AT91C_SPI0_TNCR.description="Transmit Next Counter Register"
AT91C_SPI0_TNCR.helpkey="Transmit Next Counter Register"
AT91C_SPI0_TNCR.access=memorymapped
AT91C_SPI0_TNCR.address=0xFFFE011C
AT91C_SPI0_TNCR.width=32
AT91C_SPI0_TNCR.byteEndian=little
AT91C_SPI0_RNCR.name="AT91C_SPI0_RNCR"
AT91C_SPI0_RNCR.description="Receive Next Counter Register"
AT91C_SPI0_RNCR.helpkey="Receive Next Counter Register"
AT91C_SPI0_RNCR.access=memorymapped
AT91C_SPI0_RNCR.address=0xFFFE0114
AT91C_SPI0_RNCR.width=32
AT91C_SPI0_RNCR.byteEndian=little
AT91C_SPI0_TNPR.name="AT91C_SPI0_TNPR"
AT91C_SPI0_TNPR.description="Transmit Next Pointer Register"
AT91C_SPI0_TNPR.helpkey="Transmit Next Pointer Register"
AT91C_SPI0_TNPR.access=memorymapped
AT91C_SPI0_TNPR.address=0xFFFE0118
AT91C_SPI0_TNPR.width=32
AT91C_SPI0_TNPR.byteEndian=little
# ========== Register definition for SPI0 peripheral ========== 
AT91C_SPI0_IER.name="AT91C_SPI0_IER"
AT91C_SPI0_IER.description="Interrupt Enable Register"
AT91C_SPI0_IER.helpkey="Interrupt Enable Register"
AT91C_SPI0_IER.access=memorymapped
AT91C_SPI0_IER.address=0xFFFE0014
AT91C_SPI0_IER.width=32
AT91C_SPI0_IER.byteEndian=little
AT91C_SPI0_IER.type=enum
AT91C_SPI0_IER.enum.0.name=*** Write only ***
AT91C_SPI0_IER.enum.1.name=Error
AT91C_SPI0_SR.name="AT91C_SPI0_SR"
AT91C_SPI0_SR.description="Status Register"
AT91C_SPI0_SR.helpkey="Status Register"
AT91C_SPI0_SR.access=memorymapped
AT91C_SPI0_SR.address=0xFFFE0010
AT91C_SPI0_SR.width=32
AT91C_SPI0_SR.byteEndian=little
AT91C_SPI0_SR.permission.write=none
AT91C_SPI0_IDR.name="AT91C_SPI0_IDR"
AT91C_SPI0_IDR.description="Interrupt Disable Register"
AT91C_SPI0_IDR.helpkey="Interrupt Disable Register"
AT91C_SPI0_IDR.access=memorymapped
AT91C_SPI0_IDR.address=0xFFFE0018
AT91C_SPI0_IDR.width=32
AT91C_SPI0_IDR.byteEndian=little
AT91C_SPI0_IDR.type=enum
AT91C_SPI0_IDR.enum.0.name=*** Write only ***
AT91C_SPI0_IDR.enum.1.name=Error
AT91C_SPI0_CR.name="AT91C_SPI0_CR"
AT91C_SPI0_CR.description="Control Register"
AT91C_SPI0_CR.helpkey="Control Register"
AT91C_SPI0_CR.access=memorymapped
AT91C_SPI0_CR.address=0xFFFE0000
AT91C_SPI0_CR.width=32
AT91C_SPI0_CR.byteEndian=little
AT91C_SPI0_CR.permission.write=none
AT91C_SPI0_MR.name="AT91C_SPI0_MR"
AT91C_SPI0_MR.description="Mode Register"
AT91C_SPI0_MR.helpkey="Mode Register"
AT91C_SPI0_MR.access=memorymapped
AT91C_SPI0_MR.address=0xFFFE0004
AT91C_SPI0_MR.width=32
AT91C_SPI0_MR.byteEndian=little
AT91C_SPI0_IMR.name="AT91C_SPI0_IMR"
AT91C_SPI0_IMR.description="Interrupt Mask Register"
AT91C_SPI0_IMR.helpkey="Interrupt Mask Register"
AT91C_SPI0_IMR.access=memorymapped
AT91C_SPI0_IMR.address=0xFFFE001C
AT91C_SPI0_IMR.width=32
AT91C_SPI0_IMR.byteEndian=little
AT91C_SPI0_IMR.permission.write=none
AT91C_SPI0_TDR.name="AT91C_SPI0_TDR"
AT91C_SPI0_TDR.description="Transmit Data Register"
AT91C_SPI0_TDR.helpkey="Transmit Data Register"
AT91C_SPI0_TDR.access=memorymapped
AT91C_SPI0_TDR.address=0xFFFE000C
AT91C_SPI0_TDR.width=32
AT91C_SPI0_TDR.byteEndian=little
AT91C_SPI0_TDR.type=enum
AT91C_SPI0_TDR.enum.0.name=*** Write only ***
AT91C_SPI0_TDR.enum.1.name=Error
AT91C_SPI0_RDR.name="AT91C_SPI0_RDR"
AT91C_SPI0_RDR.description="Receive Data Register"
AT91C_SPI0_RDR.helpkey="Receive Data Register"
AT91C_SPI0_RDR.access=memorymapped
AT91C_SPI0_RDR.address=0xFFFE0008
AT91C_SPI0_RDR.width=32
AT91C_SPI0_RDR.byteEndian=little
AT91C_SPI0_RDR.permission.write=none
AT91C_SPI0_CSR.name="AT91C_SPI0_CSR"
AT91C_SPI0_CSR.description="Chip Select Register"
AT91C_SPI0_CSR.helpkey="Chip Select Register"
AT91C_SPI0_CSR.access=memorymapped
AT91C_SPI0_CSR.address=0xFFFE0030
AT91C_SPI0_CSR.width=32
AT91C_SPI0_CSR.byteEndian=little
# ========== Register definition for PDC_US1 peripheral ========== 
AT91C_US1_RNCR.name="AT91C_US1_RNCR"
AT91C_US1_RNCR.description="Receive Next Counter Register"
AT91C_US1_RNCR.helpkey="Receive Next Counter Register"
AT91C_US1_RNCR.access=memorymapped
AT91C_US1_RNCR.address=0xFFFC4114
AT91C_US1_RNCR.width=32
AT91C_US1_RNCR.byteEndian=little
AT91C_US1_PTCR.name="AT91C_US1_PTCR"
AT91C_US1_PTCR.description="PDC Transfer Control Register"
AT91C_US1_PTCR.helpkey="PDC Transfer Control Register"
AT91C_US1_PTCR.access=memorymapped
AT91C_US1_PTCR.address=0xFFFC4120
AT91C_US1_PTCR.width=32
AT91C_US1_PTCR.byteEndian=little
AT91C_US1_PTCR.type=enum
AT91C_US1_PTCR.enum.0.name=*** Write only ***
AT91C_US1_PTCR.enum.1.name=Error
AT91C_US1_TCR.name="AT91C_US1_TCR"
AT91C_US1_TCR.description="Transmit Counter Register"
AT91C_US1_TCR.helpkey="Transmit Counter Register"
AT91C_US1_TCR.access=memorymapped
AT91C_US1_TCR.address=0xFFFC410C
AT91C_US1_TCR.width=32
AT91C_US1_TCR.byteEndian=little
AT91C_US1_PTSR.name="AT91C_US1_PTSR"
AT91C_US1_PTSR.description="PDC Transfer Status Register"
AT91C_US1_PTSR.helpkey="PDC Transfer Status Register"
AT91C_US1_PTSR.access=memorymapped
AT91C_US1_PTSR.address=0xFFFC4124
AT91C_US1_PTSR.width=32
AT91C_US1_PTSR.byteEndian=little
AT91C_US1_PTSR.permission.write=none
AT91C_US1_TNPR.name="AT91C_US1_TNPR"
AT91C_US1_TNPR.description="Transmit Next Pointer Register"
AT91C_US1_TNPR.helpkey="Transmit Next Pointer Register"
AT91C_US1_TNPR.access=memorymapped
AT91C_US1_TNPR.address=0xFFFC4118
AT91C_US1_TNPR.width=32
AT91C_US1_TNPR.byteEndian=little
AT91C_US1_RCR.name="AT91C_US1_RCR"
AT91C_US1_RCR.description="Receive Counter Register"
AT91C_US1_RCR.helpkey="Receive Counter Register"
AT91C_US1_RCR.access=memorymapped
AT91C_US1_RCR.address=0xFFFC4104
AT91C_US1_RCR.width=32
AT91C_US1_RCR.byteEndian=little
AT91C_US1_RNPR.name="AT91C_US1_RNPR"
AT91C_US1_RNPR.description="Receive Next Pointer Register"
AT91C_US1_RNPR.helpkey="Receive Next Pointer Register"
AT91C_US1_RNPR.access=memorymapped
AT91C_US1_RNPR.address=0xFFFC4110
AT91C_US1_RNPR.width=32
AT91C_US1_RNPR.byteEndian=little
AT91C_US1_RPR.name="AT91C_US1_RPR"
AT91C_US1_RPR.description="Receive Pointer Register"
AT91C_US1_RPR.helpkey="Receive Pointer Register"
AT91C_US1_RPR.access=memorymapped
AT91C_US1_RPR.address=0xFFFC4100
AT91C_US1_RPR.width=32
AT91C_US1_RPR.byteEndian=little
AT91C_US1_TNCR.name="AT91C_US1_TNCR"
AT91C_US1_TNCR.description="Transmit Next Counter Register"
AT91C_US1_TNCR.helpkey="Transmit Next Counter Register"
AT91C_US1_TNCR.access=memorymapped
AT91C_US1_TNCR.address=0xFFFC411C
AT91C_US1_TNCR.width=32
AT91C_US1_TNCR.byteEndian=little
AT91C_US1_TPR.name="AT91C_US1_TPR"
AT91C_US1_TPR.description="Transmit Pointer Register"
AT91C_US1_TPR.helpkey="Transmit Pointer Register"
AT91C_US1_TPR.access=memorymapped
AT91C_US1_TPR.address=0xFFFC4108
AT91C_US1_TPR.width=32
AT91C_US1_TPR.byteEndian=little
# ========== Register definition for US1 peripheral ========== 
AT91C_US1_IF.name="AT91C_US1_IF"
AT91C_US1_IF.description="IRDA_FILTER Register"
AT91C_US1_IF.helpkey="IRDA_FILTER Register"
AT91C_US1_IF.access=memorymapped
AT91C_US1_IF.address=0xFFFC404C
AT91C_US1_IF.width=32
AT91C_US1_IF.byteEndian=little
AT91C_US1_NER.name="AT91C_US1_NER"
AT91C_US1_NER.description="Nb Errors Register"
AT91C_US1_NER.helpkey="Nb Errors Register"
AT91C_US1_NER.access=memorymapped
AT91C_US1_NER.address=0xFFFC4044
AT91C_US1_NER.width=32
AT91C_US1_NER.byteEndian=little
AT91C_US1_NER.permission.write=none
AT91C_US1_RTOR.name="AT91C_US1_RTOR"
AT91C_US1_RTOR.description="Receiver Time-out Register"
AT91C_US1_RTOR.helpkey="Receiver Time-out Register"
AT91C_US1_RTOR.access=memorymapped
AT91C_US1_RTOR.address=0xFFFC4024
AT91C_US1_RTOR.width=32
AT91C_US1_RTOR.byteEndian=little
AT91C_US1_CSR.name="AT91C_US1_CSR"
AT91C_US1_CSR.description="Channel Status Register"
AT91C_US1_CSR.helpkey="Channel Status Register"
AT91C_US1_CSR.access=memorymapped
AT91C_US1_CSR.address=0xFFFC4014
AT91C_US1_CSR.width=32
AT91C_US1_CSR.byteEndian=little
AT91C_US1_CSR.permission.write=none
AT91C_US1_IDR.name="AT91C_US1_IDR"
AT91C_US1_IDR.description="Interrupt Disable Register"
AT91C_US1_IDR.helpkey="Interrupt Disable Register"
AT91C_US1_IDR.access=memorymapped
AT91C_US1_IDR.address=0xFFFC400C
AT91C_US1_IDR.width=32
AT91C_US1_IDR.byteEndian=little
AT91C_US1_IDR.type=enum
AT91C_US1_IDR.enum.0.name=*** Write only ***
AT91C_US1_IDR.enum.1.name=Error
AT91C_US1_IER.name="AT91C_US1_IER"
AT91C_US1_IER.description="Interrupt Enable Register"
AT91C_US1_IER.helpkey="Interrupt Enable Register"
AT91C_US1_IER.access=memorymapped
AT91C_US1_IER.address=0xFFFC4008
AT91C_US1_IER.width=32
AT91C_US1_IER.byteEndian=little
AT91C_US1_IER.type=enum
AT91C_US1_IER.enum.0.name=*** Write only ***
AT91C_US1_IER.enum.1.name=Error
AT91C_US1_THR.name="AT91C_US1_THR"
AT91C_US1_THR.description="Transmitter Holding Register"
AT91C_US1_THR.helpkey="Transmitter Holding Register"
AT91C_US1_THR.access=memorymapped
AT91C_US1_THR.address=0xFFFC401C
AT91C_US1_THR.width=32
AT91C_US1_THR.byteEndian=little
AT91C_US1_THR.type=enum
AT91C_US1_THR.enum.0.name=*** Write only ***
AT91C_US1_THR.enum.1.name=Error
AT91C_US1_TTGR.name="AT91C_US1_TTGR"
AT91C_US1_TTGR.description="Transmitter Time-guard Register"
AT91C_US1_TTGR.helpkey="Transmitter Time-guard Register"
AT91C_US1_TTGR.access=memorymapped
AT91C_US1_TTGR.address=0xFFFC4028
AT91C_US1_TTGR.width=32
AT91C_US1_TTGR.byteEndian=little
AT91C_US1_RHR.name="AT91C_US1_RHR"
AT91C_US1_RHR.description="Receiver Holding Register"
AT91C_US1_RHR.helpkey="Receiver Holding Register"
AT91C_US1_RHR.access=memorymapped
AT91C_US1_RHR.address=0xFFFC4018
AT91C_US1_RHR.width=32
AT91C_US1_RHR.byteEndian=little
AT91C_US1_RHR.permission.write=none
AT91C_US1_BRGR.name="AT91C_US1_BRGR"
AT91C_US1_BRGR.description="Baud Rate Generator Register"
AT91C_US1_BRGR.helpkey="Baud Rate Generator Register"
AT91C_US1_BRGR.access=memorymapped
AT91C_US1_BRGR.address=0xFFFC4020
AT91C_US1_BRGR.width=32
AT91C_US1_BRGR.byteEndian=little
AT91C_US1_IMR.name="AT91C_US1_IMR"
AT91C_US1_IMR.description="Interrupt Mask Register"
AT91C_US1_IMR.helpkey="Interrupt Mask Register"
AT91C_US1_IMR.access=memorymapped
AT91C_US1_IMR.address=0xFFFC4010
AT91C_US1_IMR.width=32
AT91C_US1_IMR.byteEndian=little
AT91C_US1_IMR.permission.write=none
AT91C_US1_FIDI.name="AT91C_US1_FIDI"
AT91C_US1_FIDI.description="FI_DI_Ratio Register"
AT91C_US1_FIDI.helpkey="FI_DI_Ratio Register"
AT91C_US1_FIDI.access=memorymapped
AT91C_US1_FIDI.address=0xFFFC4040
AT91C_US1_FIDI.width=32
AT91C_US1_FIDI.byteEndian=little
AT91C_US1_CR.name="AT91C_US1_CR"
AT91C_US1_CR.description="Control Register"
AT91C_US1_CR.helpkey="Control Register"
AT91C_US1_CR.access=memorymapped
AT91C_US1_CR.address=0xFFFC4000
AT91C_US1_CR.width=32
AT91C_US1_CR.byteEndian=little
AT91C_US1_CR.type=enum
AT91C_US1_CR.enum.0.name=*** Write only ***
AT91C_US1_CR.enum.1.name=Error
AT91C_US1_MR.name="AT91C_US1_MR"
AT91C_US1_MR.description="Mode Register"
AT91C_US1_MR.helpkey="Mode Register"
AT91C_US1_MR.access=memorymapped
AT91C_US1_MR.address=0xFFFC4004
AT91C_US1_MR.width=32
AT91C_US1_MR.byteEndian=little
# ========== Register definition for PDC_US0 peripheral ========== 
AT91C_US0_TNPR.name="AT91C_US0_TNPR"
AT91C_US0_TNPR.description="Transmit Next Pointer Register"
AT91C_US0_TNPR.helpkey="Transmit Next Pointer Register"
AT91C_US0_TNPR.access=memorymapped
AT91C_US0_TNPR.address=0xFFFC0118
AT91C_US0_TNPR.width=32
AT91C_US0_TNPR.byteEndian=little
AT91C_US0_RNPR.name="AT91C_US0_RNPR"
AT91C_US0_RNPR.description="Receive Next Pointer Register"
AT91C_US0_RNPR.helpkey="Receive Next Pointer Register"
AT91C_US0_RNPR.access=memorymapped
AT91C_US0_RNPR.address=0xFFFC0110
AT91C_US0_RNPR.width=32
AT91C_US0_RNPR.byteEndian=little
AT91C_US0_TCR.name="AT91C_US0_TCR"
AT91C_US0_TCR.description="Transmit Counter Register"
AT91C_US0_TCR.helpkey="Transmit Counter Register"
AT91C_US0_TCR.access=memorymapped
AT91C_US0_TCR.address=0xFFFC010C
AT91C_US0_TCR.width=32
AT91C_US0_TCR.byteEndian=little
AT91C_US0_PTCR.name="AT91C_US0_PTCR"
AT91C_US0_PTCR.description="PDC Transfer Control Register"
AT91C_US0_PTCR.helpkey="PDC Transfer Control Register"
AT91C_US0_PTCR.access=memorymapped
AT91C_US0_PTCR.address=0xFFFC0120
AT91C_US0_PTCR.width=32
AT91C_US0_PTCR.byteEndian=little
AT91C_US0_PTCR.type=enum
AT91C_US0_PTCR.enum.0.name=*** Write only ***
AT91C_US0_PTCR.enum.1.name=Error
AT91C_US0_PTSR.name="AT91C_US0_PTSR"
AT91C_US0_PTSR.description="PDC Transfer Status Register"
AT91C_US0_PTSR.helpkey="PDC Transfer Status Register"
AT91C_US0_PTSR.access=memorymapped
AT91C_US0_PTSR.address=0xFFFC0124
AT91C_US0_PTSR.width=32
AT91C_US0_PTSR.byteEndian=little
AT91C_US0_PTSR.permission.write=none
AT91C_US0_TNCR.name="AT91C_US0_TNCR"
AT91C_US0_TNCR.description="Transmit Next Counter Register"
AT91C_US0_TNCR.helpkey="Transmit Next Counter Register"
AT91C_US0_TNCR.access=memorymapped
AT91C_US0_TNCR.address=0xFFFC011C
AT91C_US0_TNCR.width=32
AT91C_US0_TNCR.byteEndian=little
AT91C_US0_TPR.name="AT91C_US0_TPR"
AT91C_US0_TPR.description="Transmit Pointer Register"
AT91C_US0_TPR.helpkey="Transmit Pointer Register"
AT91C_US0_TPR.access=memorymapped
AT91C_US0_TPR.address=0xFFFC0108
AT91C_US0_TPR.width=32
AT91C_US0_TPR.byteEndian=little
AT91C_US0_RCR.name="AT91C_US0_RCR"
AT91C_US0_RCR.description="Receive Counter Register"
AT91C_US0_RCR.helpkey="Receive Counter Register"
AT91C_US0_RCR.access=memorymapped
AT91C_US0_RCR.address=0xFFFC0104
AT91C_US0_RCR.width=32
AT91C_US0_RCR.byteEndian=little
AT91C_US0_RPR.name="AT91C_US0_RPR"
AT91C_US0_RPR.description="Receive Pointer Register"
AT91C_US0_RPR.helpkey="Receive Pointer Register"
AT91C_US0_RPR.access=memorymapped
AT91C_US0_RPR.address=0xFFFC0100
AT91C_US0_RPR.width=32
AT91C_US0_RPR.byteEndian=little
AT91C_US0_RNCR.name="AT91C_US0_RNCR"
AT91C_US0_RNCR.description="Receive Next Counter Register"
AT91C_US0_RNCR.helpkey="Receive Next Counter Register"
AT91C_US0_RNCR.access=memorymapped
AT91C_US0_RNCR.address=0xFFFC0114
AT91C_US0_RNCR.width=32
AT91C_US0_RNCR.byteEndian=little
# ========== Register definition for US0 peripheral ========== 
AT91C_US0_BRGR.name="AT91C_US0_BRGR"
AT91C_US0_BRGR.description="Baud Rate Generator Register"
AT91C_US0_BRGR.helpkey="Baud Rate Generator Register"
AT91C_US0_BRGR.access=memorymapped
AT91C_US0_BRGR.address=0xFFFC0020
AT91C_US0_BRGR.width=32
AT91C_US0_BRGR.byteEndian=little
AT91C_US0_NER.name="AT91C_US0_NER"
AT91C_US0_NER.description="Nb Errors Register"
AT91C_US0_NER.helpkey="Nb Errors Register"
AT91C_US0_NER.access=memorymapped
AT91C_US0_NER.address=0xFFFC0044
AT91C_US0_NER.width=32
AT91C_US0_NER.byteEndian=little
AT91C_US0_NER.permission.write=none
AT91C_US0_CR.name="AT91C_US0_CR"
AT91C_US0_CR.description="Control Register"
AT91C_US0_CR.helpkey="Control Register"
AT91C_US0_CR.access=memorymapped
AT91C_US0_CR.address=0xFFFC0000
AT91C_US0_CR.width=32
AT91C_US0_CR.byteEndian=little
AT91C_US0_CR.type=enum
AT91C_US0_CR.enum.0.name=*** Write only ***
AT91C_US0_CR.enum.1.name=Error
AT91C_US0_IMR.name="AT91C_US0_IMR"
AT91C_US0_IMR.description="Interrupt Mask Register"
AT91C_US0_IMR.helpkey="Interrupt Mask Register"
AT91C_US0_IMR.access=memorymapped
AT91C_US0_IMR.address=0xFFFC0010
AT91C_US0_IMR.width=32
AT91C_US0_IMR.byteEndian=little
AT91C_US0_IMR.permission.write=none
AT91C_US0_FIDI.name="AT91C_US0_FIDI"
AT91C_US0_FIDI.description="FI_DI_Ratio Register"
AT91C_US0_FIDI.helpkey="FI_DI_Ratio Register"
AT91C_US0_FIDI.access=memorymapped
AT91C_US0_FIDI.address=0xFFFC0040
AT91C_US0_FIDI.width=32
AT91C_US0_FIDI.byteEndian=little
AT91C_US0_TTGR.name="AT91C_US0_TTGR"
AT91C_US0_TTGR.description="Transmitter Time-guard Register"
AT91C_US0_TTGR.helpkey="Transmitter Time-guard Register"
AT91C_US0_TTGR.access=memorymapped
AT91C_US0_TTGR.address=0xFFFC0028
AT91C_US0_TTGR.width=32
AT91C_US0_TTGR.byteEndian=little
AT91C_US0_MR.name="AT91C_US0_MR"
AT91C_US0_MR.description="Mode Register"
AT91C_US0_MR.helpkey="Mode Register"
AT91C_US0_MR.access=memorymapped
AT91C_US0_MR.address=0xFFFC0004
AT91C_US0_MR.width=32
AT91C_US0_MR.byteEndian=little
AT91C_US0_RTOR.name="AT91C_US0_RTOR"
AT91C_US0_RTOR.description="Receiver Time-out Register"
AT91C_US0_RTOR.helpkey="Receiver Time-out Register"
AT91C_US0_RTOR.access=memorymapped
AT91C_US0_RTOR.address=0xFFFC0024
AT91C_US0_RTOR.width=32
AT91C_US0_RTOR.byteEndian=little
AT91C_US0_CSR.name="AT91C_US0_CSR"
AT91C_US0_CSR.description="Channel Status Register"
AT91C_US0_CSR.helpkey="Channel Status Register"
AT91C_US0_CSR.access=memorymapped
AT91C_US0_CSR.address=0xFFFC0014
AT91C_US0_CSR.width=32
AT91C_US0_CSR.byteEndian=little
AT91C_US0_CSR.permission.write=none
AT91C_US0_RHR.name="AT91C_US0_RHR"
AT91C_US0_RHR.description="Receiver Holding Register"
AT91C_US0_RHR.helpkey="Receiver Holding Register"
AT91C_US0_RHR.access=memorymapped
AT91C_US0_RHR.address=0xFFFC0018
AT91C_US0_RHR.width=32
AT91C_US0_RHR.byteEndian=little
AT91C_US0_RHR.permission.write=none
AT91C_US0_IDR.name="AT91C_US0_IDR"
AT91C_US0_IDR.description="Interrupt Disable Register"
AT91C_US0_IDR.helpkey="Interrupt Disable Register"
AT91C_US0_IDR.access=memorymapped
AT91C_US0_IDR.address=0xFFFC000C
AT91C_US0_IDR.width=32
AT91C_US0_IDR.byteEndian=little
AT91C_US0_IDR.type=enum
AT91C_US0_IDR.enum.0.name=*** Write only ***
AT91C_US0_IDR.enum.1.name=Error
AT91C_US0_THR.name="AT91C_US0_THR"
AT91C_US0_THR.description="Transmitter Holding Register"
AT91C_US0_THR.helpkey="Transmitter Holding Register"
AT91C_US0_THR.access=memorymapped
AT91C_US0_THR.address=0xFFFC001C
AT91C_US0_THR.width=32
AT91C_US0_THR.byteEndian=little
AT91C_US0_THR.type=enum
AT91C_US0_THR.enum.0.name=*** Write only ***
AT91C_US0_THR.enum.1.name=Error
AT91C_US0_IF.name="AT91C_US0_IF"
AT91C_US0_IF.description="IRDA_FILTER Register"
AT91C_US0_IF.helpkey="IRDA_FILTER Register"
AT91C_US0_IF.access=memorymapped
AT91C_US0_IF.address=0xFFFC004C
AT91C_US0_IF.width=32
AT91C_US0_IF.byteEndian=little
AT91C_US0_IER.name="AT91C_US0_IER"
AT91C_US0_IER.description="Interrupt Enable Register"
AT91C_US0_IER.helpkey="Interrupt Enable Register"
AT91C_US0_IER.access=memorymapped
AT91C_US0_IER.address=0xFFFC0008
AT91C_US0_IER.width=32
AT91C_US0_IER.byteEndian=little
AT91C_US0_IER.type=enum
AT91C_US0_IER.enum.0.name=*** Write only ***
AT91C_US0_IER.enum.1.name=Error
# ========== Register definition for PDC_SSC peripheral ========== 
AT91C_SSC_TNCR.name="AT91C_SSC_TNCR"
AT91C_SSC_TNCR.description="Transmit Next Counter Register"
AT91C_SSC_TNCR.helpkey="Transmit Next Counter Register"
AT91C_SSC_TNCR.access=memorymapped
AT91C_SSC_TNCR.address=0xFFFD411C
AT91C_SSC_TNCR.width=32
AT91C_SSC_TNCR.byteEndian=little
AT91C_SSC_RPR.name="AT91C_SSC_RPR"
AT91C_SSC_RPR.description="Receive Pointer Register"
AT91C_SSC_RPR.helpkey="Receive Pointer Register"
AT91C_SSC_RPR.access=memorymapped
AT91C_SSC_RPR.address=0xFFFD4100
AT91C_SSC_RPR.width=32
AT91C_SSC_RPR.byteEndian=little
AT91C_SSC_RNCR.name="AT91C_SSC_RNCR"
AT91C_SSC_RNCR.description="Receive Next Counter Register"
AT91C_SSC_RNCR.helpkey="Receive Next Counter Register"
AT91C_SSC_RNCR.access=memorymapped
AT91C_SSC_RNCR.address=0xFFFD4114
AT91C_SSC_RNCR.width=32
AT91C_SSC_RNCR.byteEndian=little
AT91C_SSC_TPR.name="AT91C_SSC_TPR"
AT91C_SSC_TPR.description="Transmit Pointer Register"
AT91C_SSC_TPR.helpkey="Transmit Pointer Register"
AT91C_SSC_TPR.access=memorymapped
AT91C_SSC_TPR.address=0xFFFD4108
AT91C_SSC_TPR.width=32
AT91C_SSC_TPR.byteEndian=little
AT91C_SSC_PTCR.name="AT91C_SSC_PTCR"
AT91C_SSC_PTCR.description="PDC Transfer Control Register"
AT91C_SSC_PTCR.helpkey="PDC Transfer Control Register"
AT91C_SSC_PTCR.access=memorymapped
AT91C_SSC_PTCR.address=0xFFFD4120
AT91C_SSC_PTCR.width=32
AT91C_SSC_PTCR.byteEndian=little
AT91C_SSC_PTCR.type=enum
AT91C_SSC_PTCR.enum.0.name=*** Write only ***
AT91C_SSC_PTCR.enum.1.name=Error
AT91C_SSC_TCR.name="AT91C_SSC_TCR"
AT91C_SSC_TCR.description="Transmit Counter Register"
AT91C_SSC_TCR.helpkey="Transmit Counter Register"
AT91C_SSC_TCR.access=memorymapped
AT91C_SSC_TCR.address=0xFFFD410C
AT91C_SSC_TCR.width=32
AT91C_SSC_TCR.byteEndian=little
AT91C_SSC_RCR.name="AT91C_SSC_RCR"
AT91C_SSC_RCR.description="Receive Counter Register"
AT91C_SSC_RCR.helpkey="Receive Counter Register"
AT91C_SSC_RCR.access=memorymapped
AT91C_SSC_RCR.address=0xFFFD4104
AT91C_SSC_RCR.width=32
AT91C_SSC_RCR.byteEndian=little
AT91C_SSC_RNPR.name="AT91C_SSC_RNPR"
AT91C_SSC_RNPR.description="Receive Next Pointer Register"
AT91C_SSC_RNPR.helpkey="Receive Next Pointer Register"
AT91C_SSC_RNPR.access=memorymapped
AT91C_SSC_RNPR.address=0xFFFD4110
AT91C_SSC_RNPR.width=32
AT91C_SSC_RNPR.byteEndian=little
AT91C_SSC_TNPR.name="AT91C_SSC_TNPR"
AT91C_SSC_TNPR.description="Transmit Next Pointer Register"
AT91C_SSC_TNPR.helpkey="Transmit Next Pointer Register"
AT91C_SSC_TNPR.access=memorymapped
AT91C_SSC_TNPR.address=0xFFFD4118
AT91C_SSC_TNPR.width=32
AT91C_SSC_TNPR.byteEndian=little
AT91C_SSC_PTSR.name="AT91C_SSC_PTSR"
AT91C_SSC_PTSR.description="PDC Transfer Status Register"
AT91C_SSC_PTSR.helpkey="PDC Transfer Status Register"
AT91C_SSC_PTSR.access=memorymapped
AT91C_SSC_PTSR.address=0xFFFD4124
AT91C_SSC_PTSR.width=32
AT91C_SSC_PTSR.byteEndian=little
AT91C_SSC_PTSR.permission.write=none
# ========== Register definition for SSC peripheral ========== 
AT91C_SSC_RHR.name="AT91C_SSC_RHR"
AT91C_SSC_RHR.description="Receive Holding Register"
AT91C_SSC_RHR.helpkey="Receive Holding Register"
AT91C_SSC_RHR.access=memorymapped
AT91C_SSC_RHR.address=0xFFFD4020
AT91C_SSC_RHR.width=32
AT91C_SSC_RHR.byteEndian=little
AT91C_SSC_RHR.permission.write=none
AT91C_SSC_RSHR.name="AT91C_SSC_RSHR"
AT91C_SSC_RSHR.description="Receive Sync Holding Register"
AT91C_SSC_RSHR.helpkey="Receive Sync Holding Register"
AT91C_SSC_RSHR.access=memorymapped
AT91C_SSC_RSHR.address=0xFFFD4030
AT91C_SSC_RSHR.width=32
AT91C_SSC_RSHR.byteEndian=little
AT91C_SSC_RSHR.permission.write=none
AT91C_SSC_TFMR.name="AT91C_SSC_TFMR"
AT91C_SSC_TFMR.description="Transmit Frame Mode Register"
AT91C_SSC_TFMR.helpkey="Transmit Frame Mode Register"
AT91C_SSC_TFMR.access=memorymapped
AT91C_SSC_TFMR.address=0xFFFD401C
AT91C_SSC_TFMR.width=32
AT91C_SSC_TFMR.byteEndian=little
AT91C_SSC_IDR.name="AT91C_SSC_IDR"
AT91C_SSC_IDR.description="Interrupt Disable Register"
AT91C_SSC_IDR.helpkey="Interrupt Disable Register"
AT91C_SSC_IDR.access=memorymapped
AT91C_SSC_IDR.address=0xFFFD4048
AT91C_SSC_IDR.width=32
AT91C_SSC_IDR.byteEndian=little
AT91C_SSC_IDR.type=enum
AT91C_SSC_IDR.enum.0.name=*** Write only ***
AT91C_SSC_IDR.enum.1.name=Error
AT91C_SSC_THR.name="AT91C_SSC_THR"
AT91C_SSC_THR.description="Transmit Holding Register"
AT91C_SSC_THR.helpkey="Transmit Holding Register"
AT91C_SSC_THR.access=memorymapped
AT91C_SSC_THR.address=0xFFFD4024
AT91C_SSC_THR.width=32
AT91C_SSC_THR.byteEndian=little
AT91C_SSC_THR.type=enum
AT91C_SSC_THR.enum.0.name=*** Write only ***
AT91C_SSC_THR.enum.1.name=Error
AT91C_SSC_RCMR.name="AT91C_SSC_RCMR"
AT91C_SSC_RCMR.description="Receive Clock ModeRegister"
AT91C_SSC_RCMR.helpkey="Receive Clock ModeRegister"
AT91C_SSC_RCMR.access=memorymapped
AT91C_SSC_RCMR.address=0xFFFD4010
AT91C_SSC_RCMR.width=32
AT91C_SSC_RCMR.byteEndian=little
AT91C_SSC_IER.name="AT91C_SSC_IER"
AT91C_SSC_IER.description="Interrupt Enable Register"
AT91C_SSC_IER.helpkey="Interrupt Enable Register"
AT91C_SSC_IER.access=memorymapped
AT91C_SSC_IER.address=0xFFFD4044
AT91C_SSC_IER.width=32
AT91C_SSC_IER.byteEndian=little
AT91C_SSC_IER.type=enum
AT91C_SSC_IER.enum.0.name=*** Write only ***
AT91C_SSC_IER.enum.1.name=Error
AT91C_SSC_TSHR.name="AT91C_SSC_TSHR"
AT91C_SSC_TSHR.description="Transmit Sync Holding Register"
AT91C_SSC_TSHR.helpkey="Transmit Sync Holding Register"
AT91C_SSC_TSHR.access=memorymapped
AT91C_SSC_TSHR.address=0xFFFD4034
AT91C_SSC_TSHR.width=32
AT91C_SSC_TSHR.byteEndian=little
AT91C_SSC_SR.name="AT91C_SSC_SR"
AT91C_SSC_SR.description="Status Register"
AT91C_SSC_SR.helpkey="Status Register"
AT91C_SSC_SR.access=memorymapped
AT91C_SSC_SR.address=0xFFFD4040
AT91C_SSC_SR.width=32
AT91C_SSC_SR.byteEndian=little
AT91C_SSC_SR.permission.write=none
AT91C_SSC_CMR.name="AT91C_SSC_CMR"
AT91C_SSC_CMR.description="Clock Mode Register"
AT91C_SSC_CMR.helpkey="Clock Mode Register"
AT91C_SSC_CMR.access=memorymapped
AT91C_SSC_CMR.address=0xFFFD4004
AT91C_SSC_CMR.width=32
AT91C_SSC_CMR.byteEndian=little
AT91C_SSC_TCMR.name="AT91C_SSC_TCMR"
AT91C_SSC_TCMR.description="Transmit Clock Mode Register"
AT91C_SSC_TCMR.helpkey="Transmit Clock Mode Register"
AT91C_SSC_TCMR.access=memorymapped
AT91C_SSC_TCMR.address=0xFFFD4018
AT91C_SSC_TCMR.width=32
AT91C_SSC_TCMR.byteEndian=little
AT91C_SSC_CR.name="AT91C_SSC_CR"
AT91C_SSC_CR.description="Control Register"
AT91C_SSC_CR.helpkey="Control Register"
AT91C_SSC_CR.access=memorymapped
AT91C_SSC_CR.address=0xFFFD4000
AT91C_SSC_CR.width=32
AT91C_SSC_CR.byteEndian=little
AT91C_SSC_CR.type=enum
AT91C_SSC_CR.enum.0.name=*** Write only ***
AT91C_SSC_CR.enum.1.name=Error
AT91C_SSC_IMR.name="AT91C_SSC_IMR"
AT91C_SSC_IMR.description="Interrupt Mask Register"
AT91C_SSC_IMR.helpkey="Interrupt Mask Register"
AT91C_SSC_IMR.access=memorymapped
AT91C_SSC_IMR.address=0xFFFD404C
AT91C_SSC_IMR.width=32
AT91C_SSC_IMR.byteEndian=little
AT91C_SSC_IMR.permission.write=none
AT91C_SSC_RFMR.name="AT91C_SSC_RFMR"
AT91C_SSC_RFMR.description="Receive Frame Mode Register"
AT91C_SSC_RFMR.helpkey="Receive Frame Mode Register"
AT91C_SSC_RFMR.access=memorymapped
AT91C_SSC_RFMR.address=0xFFFD4014
AT91C_SSC_RFMR.width=32
AT91C_SSC_RFMR.byteEndian=little
# ========== Register definition for TWI peripheral ========== 
AT91C_TWI_IER.name="AT91C_TWI_IER"
AT91C_TWI_IER.description="Interrupt Enable Register"
AT91C_TWI_IER.helpkey="Interrupt Enable Register"
AT91C_TWI_IER.access=memorymapped
AT91C_TWI_IER.address=0xFFFB8024
AT91C_TWI_IER.width=32
AT91C_TWI_IER.byteEndian=little
AT91C_TWI_IER.type=enum
AT91C_TWI_IER.enum.0.name=*** Write only ***
AT91C_TWI_IER.enum.1.name=Error
AT91C_TWI_CR.name="AT91C_TWI_CR"
AT91C_TWI_CR.description="Control Register"
AT91C_TWI_CR.helpkey="Control Register"
AT91C_TWI_CR.access=memorymapped
AT91C_TWI_CR.address=0xFFFB8000
AT91C_TWI_CR.width=32
AT91C_TWI_CR.byteEndian=little
AT91C_TWI_CR.type=enum
AT91C_TWI_CR.enum.0.name=*** Write only ***
AT91C_TWI_CR.enum.1.name=Error
AT91C_TWI_SR.name="AT91C_TWI_SR"
AT91C_TWI_SR.description="Status Register"
AT91C_TWI_SR.helpkey="Status Register"
AT91C_TWI_SR.access=memorymapped
AT91C_TWI_SR.address=0xFFFB8020
AT91C_TWI_SR.width=32
AT91C_TWI_SR.byteEndian=little
AT91C_TWI_SR.permission.write=none
AT91C_TWI_IMR.name="AT91C_TWI_IMR"
AT91C_TWI_IMR.description="Interrupt Mask Register"
AT91C_TWI_IMR.helpkey="Interrupt Mask Register"
AT91C_TWI_IMR.access=memorymapped
AT91C_TWI_IMR.address=0xFFFB802C
AT91C_TWI_IMR.width=32
AT91C_TWI_IMR.byteEndian=little
AT91C_TWI_IMR.permission.write=none
AT91C_TWI_THR.name="AT91C_TWI_THR"
AT91C_TWI_THR.description="Transmit Holding Register"
AT91C_TWI_THR.helpkey="Transmit Holding Register"
AT91C_TWI_THR.access=memorymapped
AT91C_TWI_THR.address=0xFFFB8034
AT91C_TWI_THR.width=32
AT91C_TWI_THR.byteEndian=little
AT91C_TWI_THR.type=enum
AT91C_TWI_THR.enum.0.name=*** Write only ***
AT91C_TWI_THR.enum.1.name=Error
AT91C_TWI_IDR.name="AT91C_TWI_IDR"
AT91C_TWI_IDR.description="Interrupt Disable Register"
AT91C_TWI_IDR.helpkey="Interrupt Disable Register"
AT91C_TWI_IDR.access=memorymapped
AT91C_TWI_IDR.address=0xFFFB8028
AT91C_TWI_IDR.width=32
AT91C_TWI_IDR.byteEndian=little
AT91C_TWI_IDR.type=enum
AT91C_TWI_IDR.enum.0.name=*** Write only ***
AT91C_TWI_IDR.enum.1.name=Error
AT91C_TWI_IADR.name="AT91C_TWI_IADR"
AT91C_TWI_IADR.description="Internal Address Register"
AT91C_TWI_IADR.helpkey="Internal Address Register"
AT91C_TWI_IADR.access=memorymapped
AT91C_TWI_IADR.address=0xFFFB800C
AT91C_TWI_IADR.width=32
AT91C_TWI_IADR.byteEndian=little
AT91C_TWI_MMR.name="AT91C_TWI_MMR"
AT91C_TWI_MMR.description="Master Mode Register"
AT91C_TWI_MMR.helpkey="Master Mode Register"
AT91C_TWI_MMR.access=memorymapped
AT91C_TWI_MMR.address=0xFFFB8004
AT91C_TWI_MMR.width=32
AT91C_TWI_MMR.byteEndian=little
AT91C_TWI_CWGR.name="AT91C_TWI_CWGR"
AT91C_TWI_CWGR.description="Clock Waveform Generator Register"
AT91C_TWI_CWGR.helpkey="Clock Waveform Generator Register"
AT91C_TWI_CWGR.access=memorymapped
AT91C_TWI_CWGR.address=0xFFFB8010
AT91C_TWI_CWGR.width=32
AT91C_TWI_CWGR.byteEndian=little
AT91C_TWI_RHR.name="AT91C_TWI_RHR"
AT91C_TWI_RHR.description="Receive Holding Register"
AT91C_TWI_RHR.helpkey="Receive Holding Register"
AT91C_TWI_RHR.access=memorymapped
AT91C_TWI_RHR.address=0xFFFB8030
AT91C_TWI_RHR.width=32
AT91C_TWI_RHR.byteEndian=little
AT91C_TWI_RHR.permission.write=none
# ========== Register definition for PWMC_CH3 peripheral ========== 
AT91C_PWMC_CH3_CUPDR.name="AT91C_PWMC_CH3_CUPDR"
AT91C_PWMC_CH3_CUPDR.description="Channel Update Register"
AT91C_PWMC_CH3_CUPDR.helpkey="Channel Update Register"
AT91C_PWMC_CH3_CUPDR.access=memorymapped
AT91C_PWMC_CH3_CUPDR.address=0xFFFCC270
AT91C_PWMC_CH3_CUPDR.width=32
AT91C_PWMC_CH3_CUPDR.byteEndian=little
AT91C_PWMC_CH3_CUPDR.type=enum
AT91C_PWMC_CH3_CUPDR.enum.0.name=*** Write only ***
AT91C_PWMC_CH3_CUPDR.enum.1.name=Error
AT91C_PWMC_CH3_Reserved.name="AT91C_PWMC_CH3_Reserved"
AT91C_PWMC_CH3_Reserved.description="Reserved"
AT91C_PWMC_CH3_Reserved.helpkey="Reserved"
AT91C_PWMC_CH3_Reserved.access=memorymapped
AT91C_PWMC_CH3_Reserved.address=0xFFFCC274
AT91C_PWMC_CH3_Reserved.width=32
AT91C_PWMC_CH3_Reserved.byteEndian=little
AT91C_PWMC_CH3_Reserved.type=enum
AT91C_PWMC_CH3_Reserved.enum.0.name=*** Write only ***
AT91C_PWMC_CH3_Reserved.enum.1.name=Error
AT91C_PWMC_CH3_CPRDR.name="AT91C_PWMC_CH3_CPRDR"
AT91C_PWMC_CH3_CPRDR.description="Channel Period Register"
AT91C_PWMC_CH3_CPRDR.helpkey="Channel Period Register"
AT91C_PWMC_CH3_CPRDR.access=memorymapped
AT91C_PWMC_CH3_CPRDR.address=0xFFFCC268
AT91C_PWMC_CH3_CPRDR.width=32
AT91C_PWMC_CH3_CPRDR.byteEndian=little
AT91C_PWMC_CH3_CDTYR.name="AT91C_PWMC_CH3_CDTYR"
AT91C_PWMC_CH3_CDTYR.description="Channel Duty Cycle Register"
AT91C_PWMC_CH3_CDTYR.helpkey="Channel Duty Cycle Register"
AT91C_PWMC_CH3_CDTYR.access=memorymapped
AT91C_PWMC_CH3_CDTYR.address=0xFFFCC264
AT91C_PWMC_CH3_CDTYR.width=32
AT91C_PWMC_CH3_CDTYR.byteEndian=little
AT91C_PWMC_CH3_CCNTR.name="AT91C_PWMC_CH3_CCNTR"
AT91C_PWMC_CH3_CCNTR.description="Channel Counter Register"
AT91C_PWMC_CH3_CCNTR.helpkey="Channel Counter Register"
AT91C_PWMC_CH3_CCNTR.access=memorymapped
AT91C_PWMC_CH3_CCNTR.address=0xFFFCC26C
AT91C_PWMC_CH3_CCNTR.width=32
AT91C_PWMC_CH3_CCNTR.byteEndian=little
AT91C_PWMC_CH3_CCNTR.permission.write=none
AT91C_PWMC_CH3_CMR.name="AT91C_PWMC_CH3_CMR"
AT91C_PWMC_CH3_CMR.description="Channel Mode Register"
AT91C_PWMC_CH3_CMR.helpkey="Channel Mode Register"
AT91C_PWMC_CH3_CMR.access=memorymapped
AT91C_PWMC_CH3_CMR.address=0xFFFCC260
AT91C_PWMC_CH3_CMR.width=32
AT91C_PWMC_CH3_CMR.byteEndian=little
# ========== Register definition for PWMC_CH2 peripheral ========== 
AT91C_PWMC_CH2_Reserved.name="AT91C_PWMC_CH2_Reserved"
AT91C_PWMC_CH2_Reserved.description="Reserved"
AT91C_PWMC_CH2_Reserved.helpkey="Reserved"
AT91C_PWMC_CH2_Reserved.access=memorymapped
AT91C_PWMC_CH2_Reserved.address=0xFFFCC254
AT91C_PWMC_CH2_Reserved.width=32
AT91C_PWMC_CH2_Reserved.byteEndian=little
AT91C_PWMC_CH2_Reserved.type=enum
AT91C_PWMC_CH2_Reserved.enum.0.name=*** Write only ***
AT91C_PWMC_CH2_Reserved.enum.1.name=Error
AT91C_PWMC_CH2_CMR.name="AT91C_PWMC_CH2_CMR"
AT91C_PWMC_CH2_CMR.description="Channel Mode Register"
AT91C_PWMC_CH2_CMR.helpkey="Channel Mode Register"
AT91C_PWMC_CH2_CMR.access=memorymapped
AT91C_PWMC_CH2_CMR.address=0xFFFCC240
AT91C_PWMC_CH2_CMR.width=32
AT91C_PWMC_CH2_CMR.byteEndian=little
AT91C_PWMC_CH2_CCNTR.name="AT91C_PWMC_CH2_CCNTR"
AT91C_PWMC_CH2_CCNTR.description="Channel Counter Register"
AT91C_PWMC_CH2_CCNTR.helpkey="Channel Counter Register"
AT91C_PWMC_CH2_CCNTR.access=memorymapped
AT91C_PWMC_CH2_CCNTR.address=0xFFFCC24C
AT91C_PWMC_CH2_CCNTR.width=32
AT91C_PWMC_CH2_CCNTR.byteEndian=little
AT91C_PWMC_CH2_CCNTR.permission.write=none
AT91C_PWMC_CH2_CPRDR.name="AT91C_PWMC_CH2_CPRDR"
AT91C_PWMC_CH2_CPRDR.description="Channel Period Register"
AT91C_PWMC_CH2_CPRDR.helpkey="Channel Period Register"
AT91C_PWMC_CH2_CPRDR.access=memorymapped
AT91C_PWMC_CH2_CPRDR.address=0xFFFCC248
AT91C_PWMC_CH2_CPRDR.width=32
AT91C_PWMC_CH2_CPRDR.byteEndian=little
AT91C_PWMC_CH2_CUPDR.name="AT91C_PWMC_CH2_CUPDR"
AT91C_PWMC_CH2_CUPDR.description="Channel Update Register"
AT91C_PWMC_CH2_CUPDR.helpkey="Channel Update Register"
AT91C_PWMC_CH2_CUPDR.access=memorymapped
AT91C_PWMC_CH2_CUPDR.address=0xFFFCC250
AT91C_PWMC_CH2_CUPDR.width=32
AT91C_PWMC_CH2_CUPDR.byteEndian=little
AT91C_PWMC_CH2_CUPDR.type=enum
AT91C_PWMC_CH2_CUPDR.enum.0.name=*** Write only ***
AT91C_PWMC_CH2_CUPDR.enum.1.name=Error
AT91C_PWMC_CH2_CDTYR.name="AT91C_PWMC_CH2_CDTYR"
AT91C_PWMC_CH2_CDTYR.description="Channel Duty Cycle Register"
AT91C_PWMC_CH2_CDTYR.helpkey="Channel Duty Cycle Register"
AT91C_PWMC_CH2_CDTYR.access=memorymapped
AT91C_PWMC_CH2_CDTYR.address=0xFFFCC244
AT91C_PWMC_CH2_CDTYR.width=32
AT91C_PWMC_CH2_CDTYR.byteEndian=little
# ========== Register definition for PWMC_CH1 peripheral ========== 
AT91C_PWMC_CH1_Reserved.name="AT91C_PWMC_CH1_Reserved"
AT91C_PWMC_CH1_Reserved.description="Reserved"
AT91C_PWMC_CH1_Reserved.helpkey="Reserved"
AT91C_PWMC_CH1_Reserved.access=memorymapped
AT91C_PWMC_CH1_Reserved.address=0xFFFCC234
AT91C_PWMC_CH1_Reserved.width=32
AT91C_PWMC_CH1_Reserved.byteEndian=little
AT91C_PWMC_CH1_Reserved.type=enum
AT91C_PWMC_CH1_Reserved.enum.0.name=*** Write only ***
AT91C_PWMC_CH1_Reserved.enum.1.name=Error
AT91C_PWMC_CH1_CUPDR.name="AT91C_PWMC_CH1_CUPDR"
AT91C_PWMC_CH1_CUPDR.description="Channel Update Register"
AT91C_PWMC_CH1_CUPDR.helpkey="Channel Update Register"
AT91C_PWMC_CH1_CUPDR.access=memorymapped
AT91C_PWMC_CH1_CUPDR.address=0xFFFCC230
AT91C_PWMC_CH1_CUPDR.width=32
AT91C_PWMC_CH1_CUPDR.byteEndian=little
AT91C_PWMC_CH1_CUPDR.type=enum
AT91C_PWMC_CH1_CUPDR.enum.0.name=*** Write only ***
AT91C_PWMC_CH1_CUPDR.enum.1.name=Error
AT91C_PWMC_CH1_CPRDR.name="AT91C_PWMC_CH1_CPRDR"
AT91C_PWMC_CH1_CPRDR.description="Channel Period Register"
AT91C_PWMC_CH1_CPRDR.helpkey="Channel Period Register"
AT91C_PWMC_CH1_CPRDR.access=memorymapped
AT91C_PWMC_CH1_CPRDR.address=0xFFFCC228
AT91C_PWMC_CH1_CPRDR.width=32
AT91C_PWMC_CH1_CPRDR.byteEndian=little
AT91C_PWMC_CH1_CCNTR.name="AT91C_PWMC_CH1_CCNTR"
AT91C_PWMC_CH1_CCNTR.description="Channel Counter Register"
AT91C_PWMC_CH1_CCNTR.helpkey="Channel Counter Register"
AT91C_PWMC_CH1_CCNTR.access=memorymapped
AT91C_PWMC_CH1_CCNTR.address=0xFFFCC22C
AT91C_PWMC_CH1_CCNTR.width=32
AT91C_PWMC_CH1_CCNTR.byteEndian=little
AT91C_PWMC_CH1_CCNTR.permission.write=none
AT91C_PWMC_CH1_CDTYR.name="AT91C_PWMC_CH1_CDTYR"
AT91C_PWMC_CH1_CDTYR.description="Channel Duty Cycle Register"
AT91C_PWMC_CH1_CDTYR.helpkey="Channel Duty Cycle Register"
AT91C_PWMC_CH1_CDTYR.access=memorymapped
AT91C_PWMC_CH1_CDTYR.address=0xFFFCC224
AT91C_PWMC_CH1_CDTYR.width=32
AT91C_PWMC_CH1_CDTYR.byteEndian=little
AT91C_PWMC_CH1_CMR.name="AT91C_PWMC_CH1_CMR"
AT91C_PWMC_CH1_CMR.description="Channel Mode Register"
AT91C_PWMC_CH1_CMR.helpkey="Channel Mode Register"
AT91C_PWMC_CH1_CMR.access=memorymapped
AT91C_PWMC_CH1_CMR.address=0xFFFCC220
AT91C_PWMC_CH1_CMR.width=32
AT91C_PWMC_CH1_CMR.byteEndian=little
# ========== Register definition for PWMC_CH0 peripheral ========== 
AT91C_PWMC_CH0_Reserved.name="AT91C_PWMC_CH0_Reserved"
AT91C_PWMC_CH0_Reserved.description="Reserved"
AT91C_PWMC_CH0_Reserved.helpkey="Reserved"
AT91C_PWMC_CH0_Reserved.access=memorymapped
AT91C_PWMC_CH0_Reserved.address=0xFFFCC214
AT91C_PWMC_CH0_Reserved.width=32
AT91C_PWMC_CH0_Reserved.byteEndian=little
AT91C_PWMC_CH0_Reserved.type=enum
AT91C_PWMC_CH0_Reserved.enum.0.name=*** Write only ***
AT91C_PWMC_CH0_Reserved.enum.1.name=Error
AT91C_PWMC_CH0_CPRDR.name="AT91C_PWMC_CH0_CPRDR"
AT91C_PWMC_CH0_CPRDR.description="Channel Period Register"
AT91C_PWMC_CH0_CPRDR.helpkey="Channel Period Register"
AT91C_PWMC_CH0_CPRDR.access=memorymapped
AT91C_PWMC_CH0_CPRDR.address=0xFFFCC208
AT91C_PWMC_CH0_CPRDR.width=32
AT91C_PWMC_CH0_CPRDR.byteEndian=little
AT91C_PWMC_CH0_CDTYR.name="AT91C_PWMC_CH0_CDTYR"
AT91C_PWMC_CH0_CDTYR.description="Channel Duty Cycle Register"
AT91C_PWMC_CH0_CDTYR.helpkey="Channel Duty Cycle Register"
AT91C_PWMC_CH0_CDTYR.access=memorymapped
AT91C_PWMC_CH0_CDTYR.address=0xFFFCC204
AT91C_PWMC_CH0_CDTYR.width=32
AT91C_PWMC_CH0_CDTYR.byteEndian=little
AT91C_PWMC_CH0_CMR.name="AT91C_PWMC_CH0_CMR"
AT91C_PWMC_CH0_CMR.description="Channel Mode Register"
AT91C_PWMC_CH0_CMR.helpkey="Channel Mode Register"
AT91C_PWMC_CH0_CMR.access=memorymapped
AT91C_PWMC_CH0_CMR.address=0xFFFCC200
AT91C_PWMC_CH0_CMR.width=32
AT91C_PWMC_CH0_CMR.byteEndian=little
AT91C_PWMC_CH0_CUPDR.name="AT91C_PWMC_CH0_CUPDR"
AT91C_PWMC_CH0_CUPDR.description="Channel Update Register"
AT91C_PWMC_CH0_CUPDR.helpkey="Channel Update Register"
AT91C_PWMC_CH0_CUPDR.access=memorymapped
AT91C_PWMC_CH0_CUPDR.address=0xFFFCC210
AT91C_PWMC_CH0_CUPDR.width=32
AT91C_PWMC_CH0_CUPDR.byteEndian=little
AT91C_PWMC_CH0_CUPDR.type=enum
AT91C_PWMC_CH0_CUPDR.enum.0.name=*** Write only ***
AT91C_PWMC_CH0_CUPDR.enum.1.name=Error
AT91C_PWMC_CH0_CCNTR.name="AT91C_PWMC_CH0_CCNTR"
AT91C_PWMC_CH0_CCNTR.description="Channel Counter Register"
AT91C_PWMC_CH0_CCNTR.helpkey="Channel Counter Register"
AT91C_PWMC_CH0_CCNTR.access=memorymapped
AT91C_PWMC_CH0_CCNTR.address=0xFFFCC20C
AT91C_PWMC_CH0_CCNTR.width=32
AT91C_PWMC_CH0_CCNTR.byteEndian=little
AT91C_PWMC_CH0_CCNTR.permission.write=none
# ========== Register definition for PWMC peripheral ========== 
AT91C_PWMC_IDR.name="AT91C_PWMC_IDR"
AT91C_PWMC_IDR.description="PWMC Interrupt Disable Register"
AT91C_PWMC_IDR.helpkey="PWMC Interrupt Disable Register"
AT91C_PWMC_IDR.access=memorymapped
AT91C_PWMC_IDR.address=0xFFFCC014
AT91C_PWMC_IDR.width=32
AT91C_PWMC_IDR.byteEndian=little
AT91C_PWMC_IDR.type=enum
AT91C_PWMC_IDR.enum.0.name=*** Write only ***
AT91C_PWMC_IDR.enum.1.name=Error
AT91C_PWMC_DIS.name="AT91C_PWMC_DIS"
AT91C_PWMC_DIS.description="PWMC Disable Register"
AT91C_PWMC_DIS.helpkey="PWMC Disable Register"
AT91C_PWMC_DIS.access=memorymapped
AT91C_PWMC_DIS.address=0xFFFCC008
AT91C_PWMC_DIS.width=32
AT91C_PWMC_DIS.byteEndian=little
AT91C_PWMC_DIS.type=enum
AT91C_PWMC_DIS.enum.0.name=*** Write only ***
AT91C_PWMC_DIS.enum.1.name=Error
AT91C_PWMC_IER.name="AT91C_PWMC_IER"
AT91C_PWMC_IER.description="PWMC Interrupt Enable Register"
AT91C_PWMC_IER.helpkey="PWMC Interrupt Enable Register"
AT91C_PWMC_IER.access=memorymapped
AT91C_PWMC_IER.address=0xFFFCC010
AT91C_PWMC_IER.width=32
AT91C_PWMC_IER.byteEndian=little
AT91C_PWMC_IER.type=enum
AT91C_PWMC_IER.enum.0.name=*** Write only ***
AT91C_PWMC_IER.enum.1.name=Error
AT91C_PWMC_VR.name="AT91C_PWMC_VR"
AT91C_PWMC_VR.description="PWMC Version Register"
AT91C_PWMC_VR.helpkey="PWMC Version Register"
AT91C_PWMC_VR.access=memorymapped
AT91C_PWMC_VR.address=0xFFFCC0FC
AT91C_PWMC_VR.width=32
AT91C_PWMC_VR.byteEndian=little
AT91C_PWMC_VR.permission.write=none
AT91C_PWMC_ISR.name="AT91C_PWMC_ISR"
AT91C_PWMC_ISR.description="PWMC Interrupt Status Register"
AT91C_PWMC_ISR.helpkey="PWMC Interrupt Status Register"
AT91C_PWMC_ISR.access=memorymapped
AT91C_PWMC_ISR.address=0xFFFCC01C
AT91C_PWMC_ISR.width=32
AT91C_PWMC_ISR.byteEndian=little
AT91C_PWMC_ISR.permission.write=none
AT91C_PWMC_SR.name="AT91C_PWMC_SR"
AT91C_PWMC_SR.description="PWMC Status Register"
AT91C_PWMC_SR.helpkey="PWMC Status Register"
AT91C_PWMC_SR.access=memorymapped
AT91C_PWMC_SR.address=0xFFFCC00C
AT91C_PWMC_SR.width=32
AT91C_PWMC_SR.byteEndian=little
AT91C_PWMC_SR.permission.write=none
AT91C_PWMC_IMR.name="AT91C_PWMC_IMR"
AT91C_PWMC_IMR.description="PWMC Interrupt Mask Register"
AT91C_PWMC_IMR.helpkey="PWMC Interrupt Mask Register"
AT91C_PWMC_IMR.access=memorymapped
AT91C_PWMC_IMR.address=0xFFFCC018
AT91C_PWMC_IMR.width=32
AT91C_PWMC_IMR.byteEndian=little
AT91C_PWMC_IMR.permission.write=none
AT91C_PWMC_MR.name="AT91C_PWMC_MR"
AT91C_PWMC_MR.description="PWMC Mode Register"
AT91C_PWMC_MR.helpkey="PWMC Mode Register"
AT91C_PWMC_MR.access=memorymapped
AT91C_PWMC_MR.address=0xFFFCC000
AT91C_PWMC_MR.width=32
AT91C_PWMC_MR.byteEndian=little
AT91C_PWMC_ENA.name="AT91C_PWMC_ENA"
AT91C_PWMC_ENA.description="PWMC Enable Register"
AT91C_PWMC_ENA.helpkey="PWMC Enable Register"
AT91C_PWMC_ENA.access=memorymapped
AT91C_PWMC_ENA.address=0xFFFCC004
AT91C_PWMC_ENA.width=32
AT91C_PWMC_ENA.byteEndian=little
AT91C_PWMC_ENA.type=enum
AT91C_PWMC_ENA.enum.0.name=*** Write only ***
AT91C_PWMC_ENA.enum.1.name=Error
# ========== Register definition for UDP peripheral ========== 
AT91C_UDP_IMR.name="AT91C_UDP_IMR"
AT91C_UDP_IMR.description="Interrupt Mask Register"
AT91C_UDP_IMR.helpkey="Interrupt Mask Register"
AT91C_UDP_IMR.access=memorymapped
AT91C_UDP_IMR.address=0xFFFB0018
AT91C_UDP_IMR.width=32
AT91C_UDP_IMR.byteEndian=little
AT91C_UDP_IMR.permission.write=none
AT91C_UDP_FADDR.name="AT91C_UDP_FADDR"
AT91C_UDP_FADDR.description="Function Address Register"
AT91C_UDP_FADDR.helpkey="Function Address Register"
AT91C_UDP_FADDR.access=memorymapped
AT91C_UDP_FADDR.address=0xFFFB0008
AT91C_UDP_FADDR.width=32
AT91C_UDP_FADDR.byteEndian=little
AT91C_UDP_NUM.name="AT91C_UDP_NUM"
AT91C_UDP_NUM.description="Frame Number Register"
AT91C_UDP_NUM.helpkey="Frame Number Register"
AT91C_UDP_NUM.access=memorymapped
AT91C_UDP_NUM.address=0xFFFB0000
AT91C_UDP_NUM.width=32
AT91C_UDP_NUM.byteEndian=little
AT91C_UDP_NUM.permission.write=none
AT91C_UDP_FDR.name="AT91C_UDP_FDR"
AT91C_UDP_FDR.description="Endpoint FIFO Data Register"
AT91C_UDP_FDR.helpkey="Endpoint FIFO Data Register"
AT91C_UDP_FDR.access=memorymapped
AT91C_UDP_FDR.address=0xFFFB0050
AT91C_UDP_FDR.width=32
AT91C_UDP_FDR.byteEndian=little
AT91C_UDP_ISR.name="AT91C_UDP_ISR"
AT91C_UDP_ISR.description="Interrupt Status Register"
AT91C_UDP_ISR.helpkey="Interrupt Status Register"
AT91C_UDP_ISR.access=memorymapped
AT91C_UDP_ISR.address=0xFFFB001C
AT91C_UDP_ISR.width=32
AT91C_UDP_ISR.byteEndian=little
AT91C_UDP_ISR.permission.write=none
AT91C_UDP_CSR.name="AT91C_UDP_CSR"
AT91C_UDP_CSR.description="Endpoint Control and Status Register"
AT91C_UDP_CSR.helpkey="Endpoint Control and Status Register"
AT91C_UDP_CSR.access=memorymapped
AT91C_UDP_CSR.address=0xFFFB0030
AT91C_UDP_CSR.width=32
AT91C_UDP_CSR.byteEndian=little
AT91C_UDP_IDR.name="AT91C_UDP_IDR"
AT91C_UDP_IDR.description="Interrupt Disable Register"
AT91C_UDP_IDR.helpkey="Interrupt Disable Register"
AT91C_UDP_IDR.access=memorymapped
AT91C_UDP_IDR.address=0xFFFB0014
AT91C_UDP_IDR.width=32
AT91C_UDP_IDR.byteEndian=little
AT91C_UDP_IDR.type=enum
AT91C_UDP_IDR.enum.0.name=*** Write only ***
AT91C_UDP_IDR.enum.1.name=Error
AT91C_UDP_ICR.name="AT91C_UDP_ICR"
AT91C_UDP_ICR.description="Interrupt Clear Register"
AT91C_UDP_ICR.helpkey="Interrupt Clear Register"
AT91C_UDP_ICR.access=memorymapped
AT91C_UDP_ICR.address=0xFFFB0020
AT91C_UDP_ICR.width=32
AT91C_UDP_ICR.byteEndian=little
AT91C_UDP_ICR.permission.write=none
AT91C_UDP_RSTEP.name="AT91C_UDP_RSTEP"
AT91C_UDP_RSTEP.description="Reset Endpoint Register"
AT91C_UDP_RSTEP.helpkey="Reset Endpoint Register"
AT91C_UDP_RSTEP.access=memorymapped
AT91C_UDP_RSTEP.address=0xFFFB0028
AT91C_UDP_RSTEP.width=32
AT91C_UDP_RSTEP.byteEndian=little
AT91C_UDP_RSTEP.permission.write=none
AT91C_UDP_TXVC.name="AT91C_UDP_TXVC"
AT91C_UDP_TXVC.description="Transceiver Control Register"
AT91C_UDP_TXVC.helpkey="Transceiver Control Register"
AT91C_UDP_TXVC.access=memorymapped
AT91C_UDP_TXVC.address=0xFFFB0074
AT91C_UDP_TXVC.width=32
AT91C_UDP_TXVC.byteEndian=little
AT91C_UDP_GLBSTATE.name="AT91C_UDP_GLBSTATE"
AT91C_UDP_GLBSTATE.description="Global State Register"
AT91C_UDP_GLBSTATE.helpkey="Global State Register"
AT91C_UDP_GLBSTATE.access=memorymapped
AT91C_UDP_GLBSTATE.address=0xFFFB0004
AT91C_UDP_GLBSTATE.width=32
AT91C_UDP_GLBSTATE.byteEndian=little
AT91C_UDP_IER.name="AT91C_UDP_IER"
AT91C_UDP_IER.description="Interrupt Enable Register"
AT91C_UDP_IER.helpkey="Interrupt Enable Register"
AT91C_UDP_IER.access=memorymapped
AT91C_UDP_IER.address=0xFFFB0010
AT91C_UDP_IER.width=32
AT91C_UDP_IER.byteEndian=little
AT91C_UDP_IER.type=enum
AT91C_UDP_IER.enum.0.name=*** Write only ***
AT91C_UDP_IER.enum.1.name=Error
# ========== Register definition for TC0 peripheral ========== 
AT91C_TC0_SR.name="AT91C_TC0_SR"
AT91C_TC0_SR.description="Status Register"
AT91C_TC0_SR.helpkey="Status Register"
AT91C_TC0_SR.access=memorymapped
AT91C_TC0_SR.address=0xFFFA0020
AT91C_TC0_SR.width=32
AT91C_TC0_SR.byteEndian=little
AT91C_TC0_SR.permission.write=none
AT91C_TC0_RC.name="AT91C_TC0_RC"
AT91C_TC0_RC.description="Register C"
AT91C_TC0_RC.helpkey="Register C"
AT91C_TC0_RC.access=memorymapped
AT91C_TC0_RC.address=0xFFFA001C
AT91C_TC0_RC.width=32
AT91C_TC0_RC.byteEndian=little
AT91C_TC0_RB.name="AT91C_TC0_RB"
AT91C_TC0_RB.description="Register B"
AT91C_TC0_RB.helpkey="Register B"
AT91C_TC0_RB.access=memorymapped
AT91C_TC0_RB.address=0xFFFA0018
AT91C_TC0_RB.width=32
AT91C_TC0_RB.byteEndian=little
AT91C_TC0_CCR.name="AT91C_TC0_CCR"
AT91C_TC0_CCR.description="Channel Control Register"
AT91C_TC0_CCR.helpkey="Channel Control Register"
AT91C_TC0_CCR.access=memorymapped
AT91C_TC0_CCR.address=0xFFFA0000
AT91C_TC0_CCR.width=32
AT91C_TC0_CCR.byteEndian=little
AT91C_TC0_CCR.type=enum
AT91C_TC0_CCR.enum.0.name=*** Write only ***
AT91C_TC0_CCR.enum.1.name=Error
AT91C_TC0_CMR.name="AT91C_TC0_CMR"
AT91C_TC0_CMR.description="Channel Mode Register (Capture Mode / Waveform Mode)"
AT91C_TC0_CMR.helpkey="Channel Mode Register (Capture Mode / Waveform Mode)"
AT91C_TC0_CMR.access=memorymapped
AT91C_TC0_CMR.address=0xFFFA0004
AT91C_TC0_CMR.width=32
AT91C_TC0_CMR.byteEndian=little
AT91C_TC0_IER.name="AT91C_TC0_IER"
AT91C_TC0_IER.description="Interrupt Enable Register"
AT91C_TC0_IER.helpkey="Interrupt Enable Register"
AT91C_TC0_IER.access=memorymapped
AT91C_TC0_IER.address=0xFFFA0024
AT91C_TC0_IER.width=32
AT91C_TC0_IER.byteEndian=little
AT91C_TC0_IER.type=enum
AT91C_TC0_IER.enum.0.name=*** Write only ***
AT91C_TC0_IER.enum.1.name=Error
AT91C_TC0_RA.name="AT91C_TC0_RA"
AT91C_TC0_RA.description="Register A"
AT91C_TC0_RA.helpkey="Register A"
AT91C_TC0_RA.access=memorymapped
AT91C_TC0_RA.address=0xFFFA0014
AT91C_TC0_RA.width=32
AT91C_TC0_RA.byteEndian=little
AT91C_TC0_IDR.name="AT91C_TC0_IDR"
AT91C_TC0_IDR.description="Interrupt Disable Register"
AT91C_TC0_IDR.helpkey="Interrupt Disable Register"
AT91C_TC0_IDR.access=memorymapped
AT91C_TC0_IDR.address=0xFFFA0028
AT91C_TC0_IDR.width=32
AT91C_TC0_IDR.byteEndian=little
AT91C_TC0_IDR.type=enum
AT91C_TC0_IDR.enum.0.name=*** Write only ***
AT91C_TC0_IDR.enum.1.name=Error
AT91C_TC0_CV.name="AT91C_TC0_CV"
AT91C_TC0_CV.description="Counter Value"
AT91C_TC0_CV.helpkey="Counter Value"
AT91C_TC0_CV.access=memorymapped
AT91C_TC0_CV.address=0xFFFA0010
AT91C_TC0_CV.width=32
AT91C_TC0_CV.byteEndian=little
AT91C_TC0_IMR.name="AT91C_TC0_IMR"
AT91C_TC0_IMR.description="Interrupt Mask Register"
AT91C_TC0_IMR.helpkey="Interrupt Mask Register"
AT91C_TC0_IMR.access=memorymapped
AT91C_TC0_IMR.address=0xFFFA002C
AT91C_TC0_IMR.width=32
AT91C_TC0_IMR.byteEndian=little
AT91C_TC0_IMR.permission.write=none
# ========== Register definition for TC1 peripheral ========== 
AT91C_TC1_RB.name="AT91C_TC1_RB"
AT91C_TC1_RB.description="Register B"
AT91C_TC1_RB.helpkey="Register B"
AT91C_TC1_RB.access=memorymapped
AT91C_TC1_RB.address=0xFFFA0058
AT91C_TC1_RB.width=32
AT91C_TC1_RB.byteEndian=little
AT91C_TC1_CCR.name="AT91C_TC1_CCR"
AT91C_TC1_CCR.description="Channel Control Register"
AT91C_TC1_CCR.helpkey="Channel Control Register"
AT91C_TC1_CCR.access=memorymapped
AT91C_TC1_CCR.address=0xFFFA0040
AT91C_TC1_CCR.width=32
AT91C_TC1_CCR.byteEndian=little
AT91C_TC1_CCR.type=enum
AT91C_TC1_CCR.enum.0.name=*** Write only ***
AT91C_TC1_CCR.enum.1.name=Error
AT91C_TC1_IER.name="AT91C_TC1_IER"
AT91C_TC1_IER.description="Interrupt Enable Register"
AT91C_TC1_IER.helpkey="Interrupt Enable Register"
AT91C_TC1_IER.access=memorymapped
AT91C_TC1_IER.address=0xFFFA0064
AT91C_TC1_IER.width=32
AT91C_TC1_IER.byteEndian=little
AT91C_TC1_IER.type=enum
AT91C_TC1_IER.enum.0.name=*** Write only ***
AT91C_TC1_IER.enum.1.name=Error
AT91C_TC1_IDR.name="AT91C_TC1_IDR"
AT91C_TC1_IDR.description="Interrupt Disable Register"
AT91C_TC1_IDR.helpkey="Interrupt Disable Register"
AT91C_TC1_IDR.access=memorymapped
AT91C_TC1_IDR.address=0xFFFA0068
AT91C_TC1_IDR.width=32
AT91C_TC1_IDR.byteEndian=little
AT91C_TC1_IDR.type=enum
AT91C_TC1_IDR.enum.0.name=*** Write only ***
AT91C_TC1_IDR.enum.1.name=Error
AT91C_TC1_SR.name="AT91C_TC1_SR"
AT91C_TC1_SR.description="Status Register"
AT91C_TC1_SR.helpkey="Status Register"
AT91C_TC1_SR.access=memorymapped
AT91C_TC1_SR.address=0xFFFA0060
AT91C_TC1_SR.width=32
AT91C_TC1_SR.byteEndian=little
AT91C_TC1_SR.permission.write=none
AT91C_TC1_CMR.name="AT91C_TC1_CMR"
AT91C_TC1_CMR.description="Channel Mode Register (Capture Mode / Waveform Mode)"
AT91C_TC1_CMR.helpkey="Channel Mode Register (Capture Mode / Waveform Mode)"
AT91C_TC1_CMR.access=memorymapped
AT91C_TC1_CMR.address=0xFFFA0044
AT91C_TC1_CMR.width=32
AT91C_TC1_CMR.byteEndian=little
AT91C_TC1_RA.name="AT91C_TC1_RA"
AT91C_TC1_RA.description="Register A"
AT91C_TC1_RA.helpkey="Register A"
AT91C_TC1_RA.access=memorymapped
AT91C_TC1_RA.address=0xFFFA0054
AT91C_TC1_RA.width=32
AT91C_TC1_RA.byteEndian=little
AT91C_TC1_RC.name="AT91C_TC1_RC"
AT91C_TC1_RC.description="Register C"
AT91C_TC1_RC.helpkey="Register C"
AT91C_TC1_RC.access=memorymapped
AT91C_TC1_RC.address=0xFFFA005C
AT91C_TC1_RC.width=32
AT91C_TC1_RC.byteEndian=little
AT91C_TC1_IMR.name="AT91C_TC1_IMR"
AT91C_TC1_IMR.description="Interrupt Mask Register"
AT91C_TC1_IMR.helpkey="Interrupt Mask Register"
AT91C_TC1_IMR.access=memorymapped
AT91C_TC1_IMR.address=0xFFFA006C
AT91C_TC1_IMR.width=32
AT91C_TC1_IMR.byteEndian=little
AT91C_TC1_IMR.permission.write=none
AT91C_TC1_CV.name="AT91C_TC1_CV"
AT91C_TC1_CV.description="Counter Value"
AT91C_TC1_CV.helpkey="Counter Value"
AT91C_TC1_CV.access=memorymapped
AT91C_TC1_CV.address=0xFFFA0050
AT91C_TC1_CV.width=32
AT91C_TC1_CV.byteEndian=little
# ========== Register definition for TC2 peripheral ========== 
AT91C_TC2_CMR.name="AT91C_TC2_CMR"
AT91C_TC2_CMR.description="Channel Mode Register (Capture Mode / Waveform Mode)"
AT91C_TC2_CMR.helpkey="Channel Mode Register (Capture Mode / Waveform Mode)"
AT91C_TC2_CMR.access=memorymapped
AT91C_TC2_CMR.address=0xFFFA0084
AT91C_TC2_CMR.width=32
AT91C_TC2_CMR.byteEndian=little
AT91C_TC2_CCR.name="AT91C_TC2_CCR"
AT91C_TC2_CCR.description="Channel Control Register"
AT91C_TC2_CCR.helpkey="Channel Control Register"
AT91C_TC2_CCR.access=memorymapped
AT91C_TC2_CCR.address=0xFFFA0080
AT91C_TC2_CCR.width=32
AT91C_TC2_CCR.byteEndian=little
AT91C_TC2_CCR.type=enum
AT91C_TC2_CCR.enum.0.name=*** Write only ***
AT91C_TC2_CCR.enum.1.name=Error
AT91C_TC2_CV.name="AT91C_TC2_CV"
AT91C_TC2_CV.description="Counter Value"
AT91C_TC2_CV.helpkey="Counter Value"
AT91C_TC2_CV.access=memorymapped
AT91C_TC2_CV.address=0xFFFA0090
AT91C_TC2_CV.width=32
AT91C_TC2_CV.byteEndian=little
AT91C_TC2_RA.name="AT91C_TC2_RA"
AT91C_TC2_RA.description="Register A"
AT91C_TC2_RA.helpkey="Register A"
AT91C_TC2_RA.access=memorymapped
AT91C_TC2_RA.address=0xFFFA0094
AT91C_TC2_RA.width=32
AT91C_TC2_RA.byteEndian=little
AT91C_TC2_RB.name="AT91C_TC2_RB"
AT91C_TC2_RB.description="Register B"
AT91C_TC2_RB.helpkey="Register B"
AT91C_TC2_RB.access=memorymapped
AT91C_TC2_RB.address=0xFFFA0098
AT91C_TC2_RB.width=32
AT91C_TC2_RB.byteEndian=little
AT91C_TC2_IDR.name="AT91C_TC2_IDR"
AT91C_TC2_IDR.description="Interrupt Disable Register"
AT91C_TC2_IDR.helpkey="Interrupt Disable Register"
AT91C_TC2_IDR.access=memorymapped
AT91C_TC2_IDR.address=0xFFFA00A8
AT91C_TC2_IDR.width=32
AT91C_TC2_IDR.byteEndian=little
AT91C_TC2_IDR.type=enum
AT91C_TC2_IDR.enum.0.name=*** Write only ***
AT91C_TC2_IDR.enum.1.name=Error
AT91C_TC2_IMR.name="AT91C_TC2_IMR"
AT91C_TC2_IMR.description="Interrupt Mask Register"
AT91C_TC2_IMR.helpkey="Interrupt Mask Register"
AT91C_TC2_IMR.access=memorymapped
AT91C_TC2_IMR.address=0xFFFA00AC
AT91C_TC2_IMR.width=32
AT91C_TC2_IMR.byteEndian=little
AT91C_TC2_IMR.permission.write=none
AT91C_TC2_RC.name="AT91C_TC2_RC"
AT91C_TC2_RC.description="Register C"
AT91C_TC2_RC.helpkey="Register C"
AT91C_TC2_RC.access=memorymapped
AT91C_TC2_RC.address=0xFFFA009C
AT91C_TC2_RC.width=32
AT91C_TC2_RC.byteEndian=little
AT91C_TC2_IER.name="AT91C_TC2_IER"
AT91C_TC2_IER.description="Interrupt Enable Register"
AT91C_TC2_IER.helpkey="Interrupt Enable Register"
AT91C_TC2_IER.access=memorymapped
AT91C_TC2_IER.address=0xFFFA00A4
AT91C_TC2_IER.width=32
AT91C_TC2_IER.byteEndian=little
AT91C_TC2_IER.type=enum
AT91C_TC2_IER.enum.0.name=*** Write only ***
AT91C_TC2_IER.enum.1.name=Error
AT91C_TC2_SR.name="AT91C_TC2_SR"
AT91C_TC2_SR.description="Status Register"
AT91C_TC2_SR.helpkey="Status Register"
AT91C_TC2_SR.access=memorymapped
AT91C_TC2_SR.address=0xFFFA00A0
AT91C_TC2_SR.width=32
AT91C_TC2_SR.byteEndian=little
AT91C_TC2_SR.permission.write=none
# ========== Register definition for TCB peripheral ========== 
AT91C_TCB_BMR.name="AT91C_TCB_BMR"
AT91C_TCB_BMR.description="TC Block Mode Register"
AT91C_TCB_BMR.helpkey="TC Block Mode Register"
AT91C_TCB_BMR.access=memorymapped
AT91C_TCB_BMR.address=0xFFFA00C4
AT91C_TCB_BMR.width=32
AT91C_TCB_BMR.byteEndian=little
AT91C_TCB_BCR.name="AT91C_TCB_BCR"
AT91C_TCB_BCR.description="TC Block Control Register"
AT91C_TCB_BCR.helpkey="TC Block Control Register"
AT91C_TCB_BCR.access=memorymapped
AT91C_TCB_BCR.address=0xFFFA00C0
AT91C_TCB_BCR.width=32
AT91C_TCB_BCR.byteEndian=little
AT91C_TCB_BCR.type=enum
AT91C_TCB_BCR.enum.0.name=*** Write only ***
AT91C_TCB_BCR.enum.1.name=Error
# ========== Register definition for CAN_MB0 peripheral ========== 
AT91C_CAN_MB0_MDL.name="AT91C_CAN_MB0_MDL"
AT91C_CAN_MB0_MDL.description="MailBox Data Low Register"
AT91C_CAN_MB0_MDL.helpkey="MailBox Data Low Register"
AT91C_CAN_MB0_MDL.access=memorymapped
AT91C_CAN_MB0_MDL.address=0xFFFD0214
AT91C_CAN_MB0_MDL.width=32
AT91C_CAN_MB0_MDL.byteEndian=little
AT91C_CAN_MB0_MAM.name="AT91C_CAN_MB0_MAM"
AT91C_CAN_MB0_MAM.description="MailBox Acceptance Mask Register"
AT91C_CAN_MB0_MAM.helpkey="MailBox Acceptance Mask Register"
AT91C_CAN_MB0_MAM.access=memorymapped
AT91C_CAN_MB0_MAM.address=0xFFFD0204
AT91C_CAN_MB0_MAM.width=32
AT91C_CAN_MB0_MAM.byteEndian=little
AT91C_CAN_MB0_MCR.name="AT91C_CAN_MB0_MCR"
AT91C_CAN_MB0_MCR.description="MailBox Control Register"
AT91C_CAN_MB0_MCR.helpkey="MailBox Control Register"
AT91C_CAN_MB0_MCR.access=memorymapped
AT91C_CAN_MB0_MCR.address=0xFFFD021C
AT91C_CAN_MB0_MCR.width=32
AT91C_CAN_MB0_MCR.byteEndian=little
AT91C_CAN_MB0_MCR.type=enum
AT91C_CAN_MB0_MCR.enum.0.name=*** Write only ***
AT91C_CAN_MB0_MCR.enum.1.name=Error
AT91C_CAN_MB0_MID.name="AT91C_CAN_MB0_MID"
AT91C_CAN_MB0_MID.description="MailBox ID Register"
AT91C_CAN_MB0_MID.helpkey="MailBox ID Register"
AT91C_CAN_MB0_MID.access=memorymapped
AT91C_CAN_MB0_MID.address=0xFFFD0208
AT91C_CAN_MB0_MID.width=32
AT91C_CAN_MB0_MID.byteEndian=little
AT91C_CAN_MB0_MSR.name="AT91C_CAN_MB0_MSR"
AT91C_CAN_MB0_MSR.description="MailBox Status Register"
AT91C_CAN_MB0_MSR.helpkey="MailBox Status Register"
AT91C_CAN_MB0_MSR.access=memorymapped
AT91C_CAN_MB0_MSR.address=0xFFFD0210
AT91C_CAN_MB0_MSR.width=32
AT91C_CAN_MB0_MSR.byteEndian=little
AT91C_CAN_MB0_MSR.permission.write=none
AT91C_CAN_MB0_MFID.name="AT91C_CAN_MB0_MFID"
AT91C_CAN_MB0_MFID.description="MailBox Family ID Register"
AT91C_CAN_MB0_MFID.helpkey="MailBox Family ID Register"
AT91C_CAN_MB0_MFID.access=memorymapped
AT91C_CAN_MB0_MFID.address=0xFFFD020C
AT91C_CAN_MB0_MFID.width=32
AT91C_CAN_MB0_MFID.byteEndian=little
AT91C_CAN_MB0_MFID.permission.write=none
AT91C_CAN_MB0_MDH.name="AT91C_CAN_MB0_MDH"
AT91C_CAN_MB0_MDH.description="MailBox Data High Register"
AT91C_CAN_MB0_MDH.helpkey="MailBox Data High Register"
AT91C_CAN_MB0_MDH.access=memorymapped
AT91C_CAN_MB0_MDH.address=0xFFFD0218
AT91C_CAN_MB0_MDH.width=32
AT91C_CAN_MB0_MDH.byteEndian=little
AT91C_CAN_MB0_MMR.name="AT91C_CAN_MB0_MMR"
AT91C_CAN_MB0_MMR.description="MailBox Mode Register"
AT91C_CAN_MB0_MMR.helpkey="MailBox Mode Register"
AT91C_CAN_MB0_MMR.access=memorymapped
AT91C_CAN_MB0_MMR.address=0xFFFD0200
AT91C_CAN_MB0_MMR.width=32
AT91C_CAN_MB0_MMR.byteEndian=little
# ========== Register definition for CAN_MB1 peripheral ========== 
AT91C_CAN_MB1_MDL.name="AT91C_CAN_MB1_MDL"
AT91C_CAN_MB1_MDL.description="MailBox Data Low Register"
AT91C_CAN_MB1_MDL.helpkey="MailBox Data Low Register"
AT91C_CAN_MB1_MDL.access=memorymapped
AT91C_CAN_MB1_MDL.address=0xFFFD0234
AT91C_CAN_MB1_MDL.width=32
AT91C_CAN_MB1_MDL.byteEndian=little
AT91C_CAN_MB1_MID.name="AT91C_CAN_MB1_MID"
AT91C_CAN_MB1_MID.description="MailBox ID Register"
AT91C_CAN_MB1_MID.helpkey="MailBox ID Register"
AT91C_CAN_MB1_MID.access=memorymapped
AT91C_CAN_MB1_MID.address=0xFFFD0228
AT91C_CAN_MB1_MID.width=32
AT91C_CAN_MB1_MID.byteEndian=little
AT91C_CAN_MB1_MMR.name="AT91C_CAN_MB1_MMR"
AT91C_CAN_MB1_MMR.description="MailBox Mode Register"
AT91C_CAN_MB1_MMR.helpkey="MailBox Mode Register"
AT91C_CAN_MB1_MMR.access=memorymapped
AT91C_CAN_MB1_MMR.address=0xFFFD0220
AT91C_CAN_MB1_MMR.width=32
AT91C_CAN_MB1_MMR.byteEndian=little
AT91C_CAN_MB1_MSR.name="AT91C_CAN_MB1_MSR"
AT91C_CAN_MB1_MSR.description="MailBox Status Register"
AT91C_CAN_MB1_MSR.helpkey="MailBox Status Register"
AT91C_CAN_MB1_MSR.access=memorymapped
AT91C_CAN_MB1_MSR.address=0xFFFD0230
AT91C_CAN_MB1_MSR.width=32
AT91C_CAN_MB1_MSR.byteEndian=little
AT91C_CAN_MB1_MSR.permission.write=none
AT91C_CAN_MB1_MAM.name="AT91C_CAN_MB1_MAM"
AT91C_CAN_MB1_MAM.description="MailBox Acceptance Mask Register"
AT91C_CAN_MB1_MAM.helpkey="MailBox Acceptance Mask Register"
AT91C_CAN_MB1_MAM.access=memorymapped
AT91C_CAN_MB1_MAM.address=0xFFFD0224
AT91C_CAN_MB1_MAM.width=32
AT91C_CAN_MB1_MAM.byteEndian=little
AT91C_CAN_MB1_MDH.name="AT91C_CAN_MB1_MDH"
AT91C_CAN_MB1_MDH.description="MailBox Data High Register"
AT91C_CAN_MB1_MDH.helpkey="MailBox Data High Register"
AT91C_CAN_MB1_MDH.access=memorymapped
AT91C_CAN_MB1_MDH.address=0xFFFD0238
AT91C_CAN_MB1_MDH.width=32
AT91C_CAN_MB1_MDH.byteEndian=little
AT91C_CAN_MB1_MCR.name="AT91C_CAN_MB1_MCR"
AT91C_CAN_MB1_MCR.description="MailBox Control Register"
AT91C_CAN_MB1_MCR.helpkey="MailBox Control Register"
AT91C_CAN_MB1_MCR.access=memorymapped
AT91C_CAN_MB1_MCR.address=0xFFFD023C
AT91C_CAN_MB1_MCR.width=32
AT91C_CAN_MB1_MCR.byteEndian=little
AT91C_CAN_MB1_MCR.type=enum
AT91C_CAN_MB1_MCR.enum.0.name=*** Write only ***
AT91C_CAN_MB1_MCR.enum.1.name=Error
AT91C_CAN_MB1_MFID.name="AT91C_CAN_MB1_MFID"
AT91C_CAN_MB1_MFID.description="MailBox Family ID Register"
AT91C_CAN_MB1_MFID.helpkey="MailBox Family ID Register"
AT91C_CAN_MB1_MFID.access=memorymapped
AT91C_CAN_MB1_MFID.address=0xFFFD022C
AT91C_CAN_MB1_MFID.width=32
AT91C_CAN_MB1_MFID.byteEndian=little
AT91C_CAN_MB1_MFID.permission.write=none
# ========== Register definition for CAN_MB2 peripheral ========== 
AT91C_CAN_MB2_MCR.name="AT91C_CAN_MB2_MCR"
AT91C_CAN_MB2_MCR.description="MailBox Control Register"
AT91C_CAN_MB2_MCR.helpkey="MailBox Control Register"
AT91C_CAN_MB2_MCR.access=memorymapped
AT91C_CAN_MB2_MCR.address=0xFFFD025C
AT91C_CAN_MB2_MCR.width=32
AT91C_CAN_MB2_MCR.byteEndian=little
AT91C_CAN_MB2_MCR.type=enum
AT91C_CAN_MB2_MCR.enum.0.name=*** Write only ***
AT91C_CAN_MB2_MCR.enum.1.name=Error
AT91C_CAN_MB2_MDH.name="AT91C_CAN_MB2_MDH"
AT91C_CAN_MB2_MDH.description="MailBox Data High Register"
AT91C_CAN_MB2_MDH.helpkey="MailBox Data High Register"
AT91C_CAN_MB2_MDH.access=memorymapped
AT91C_CAN_MB2_MDH.address=0xFFFD0258
AT91C_CAN_MB2_MDH.width=32
AT91C_CAN_MB2_MDH.byteEndian=little
AT91C_CAN_MB2_MID.name="AT91C_CAN_MB2_MID"
AT91C_CAN_MB2_MID.description="MailBox ID Register"
AT91C_CAN_MB2_MID.helpkey="MailBox ID Register"
AT91C_CAN_MB2_MID.access=memorymapped
AT91C_CAN_MB2_MID.address=0xFFFD0248
AT91C_CAN_MB2_MID.width=32
AT91C_CAN_MB2_MID.byteEndian=little
AT91C_CAN_MB2_MDL.name="AT91C_CAN_MB2_MDL"
AT91C_CAN_MB2_MDL.description="MailBox Data Low Register"
AT91C_CAN_MB2_MDL.helpkey="MailBox Data Low Register"
AT91C_CAN_MB2_MDL.access=memorymapped
AT91C_CAN_MB2_MDL.address=0xFFFD0254
AT91C_CAN_MB2_MDL.width=32
AT91C_CAN_MB2_MDL.byteEndian=little
AT91C_CAN_MB2_MMR.name="AT91C_CAN_MB2_MMR"
AT91C_CAN_MB2_MMR.description="MailBox Mode Register"
AT91C_CAN_MB2_MMR.helpkey="MailBox Mode Register"
AT91C_CAN_MB2_MMR.access=memorymapped
AT91C_CAN_MB2_MMR.address=0xFFFD0240
AT91C_CAN_MB2_MMR.width=32
AT91C_CAN_MB2_MMR.byteEndian=little
AT91C_CAN_MB2_MAM.name="AT91C_CAN_MB2_MAM"
AT91C_CAN_MB2_MAM.description="MailBox Acceptance Mask Register"
AT91C_CAN_MB2_MAM.helpkey="MailBox Acceptance Mask Register"
AT91C_CAN_MB2_MAM.access=memorymapped
AT91C_CAN_MB2_MAM.address=0xFFFD0244
AT91C_CAN_MB2_MAM.width=32
AT91C_CAN_MB2_MAM.byteEndian=little
AT91C_CAN_MB2_MFID.name="AT91C_CAN_MB2_MFID"
AT91C_CAN_MB2_MFID.description="MailBox Family ID Register"
AT91C_CAN_MB2_MFID.helpkey="MailBox Family ID Register"
AT91C_CAN_MB2_MFID.access=memorymapped
AT91C_CAN_MB2_MFID.address=0xFFFD024C
AT91C_CAN_MB2_MFID.width=32
AT91C_CAN_MB2_MFID.byteEndian=little
AT91C_CAN_MB2_MFID.permission.write=none
AT91C_CAN_MB2_MSR.name="AT91C_CAN_MB2_MSR"
AT91C_CAN_MB2_MSR.description="MailBox Status Register"
AT91C_CAN_MB2_MSR.helpkey="MailBox Status Register"
AT91C_CAN_MB2_MSR.access=memorymapped
AT91C_CAN_MB2_MSR.address=0xFFFD0250
AT91C_CAN_MB2_MSR.width=32
AT91C_CAN_MB2_MSR.byteEndian=little
AT91C_CAN_MB2_MSR.permission.write=none
# ========== Register definition for CAN_MB3 peripheral ========== 
AT91C_CAN_MB3_MFID.name="AT91C_CAN_MB3_MFID"
AT91C_CAN_MB3_MFID.description="MailBox Family ID Register"
AT91C_CAN_MB3_MFID.helpkey="MailBox Family ID Register"
AT91C_CAN_MB3_MFID.access=memorymapped
AT91C_CAN_MB3_MFID.address=0xFFFD026C
AT91C_CAN_MB3_MFID.width=32
AT91C_CAN_MB3_MFID.byteEndian=little
AT91C_CAN_MB3_MFID.permission.write=none
AT91C_CAN_MB3_MAM.name="AT91C_CAN_MB3_MAM"
AT91C_CAN_MB3_MAM.description="MailBox Acceptance Mask Register"
AT91C_CAN_MB3_MAM.helpkey="MailBox Acceptance Mask Register"
AT91C_CAN_MB3_MAM.access=memorymapped
AT91C_CAN_MB3_MAM.address=0xFFFD0264
AT91C_CAN_MB3_MAM.width=32
AT91C_CAN_MB3_MAM.byteEndian=little
AT91C_CAN_MB3_MID.name="AT91C_CAN_MB3_MID"
AT91C_CAN_MB3_MID.description="MailBox ID Register"
AT91C_CAN_MB3_MID.helpkey="MailBox ID Register"
AT91C_CAN_MB3_MID.access=memorymapped
AT91C_CAN_MB3_MID.address=0xFFFD0268
AT91C_CAN_MB3_MID.width=32
AT91C_CAN_MB3_MID.byteEndian=little
AT91C_CAN_MB3_MCR.name="AT91C_CAN_MB3_MCR"
AT91C_CAN_MB3_MCR.description="MailBox Control Register"
AT91C_CAN_MB3_MCR.helpkey="MailBox Control Register"
AT91C_CAN_MB3_MCR.access=memorymapped
AT91C_CAN_MB3_MCR.address=0xFFFD027C
AT91C_CAN_MB3_MCR.width=32
AT91C_CAN_MB3_MCR.byteEndian=little
AT91C_CAN_MB3_MCR.type=enum
AT91C_CAN_MB3_MCR.enum.0.name=*** Write only ***
AT91C_CAN_MB3_MCR.enum.1.name=Error
AT91C_CAN_MB3_MMR.name="AT91C_CAN_MB3_MMR"
AT91C_CAN_MB3_MMR.description="MailBox Mode Register"
AT91C_CAN_MB3_MMR.helpkey="MailBox Mode Register"
AT91C_CAN_MB3_MMR.access=memorymapped
AT91C_CAN_MB3_MMR.address=0xFFFD0260
AT91C_CAN_MB3_MMR.width=32
AT91C_CAN_MB3_MMR.byteEndian=little
AT91C_CAN_MB3_MSR.name="AT91C_CAN_MB3_MSR"
AT91C_CAN_MB3_MSR.description="MailBox Status Register"
AT91C_CAN_MB3_MSR.helpkey="MailBox Status Register"
AT91C_CAN_MB3_MSR.access=memorymapped
AT91C_CAN_MB3_MSR.address=0xFFFD0270
AT91C_CAN_MB3_MSR.width=32
AT91C_CAN_MB3_MSR.byteEndian=little
AT91C_CAN_MB3_MSR.permission.write=none
AT91C_CAN_MB3_MDL.name="AT91C_CAN_MB3_MDL"
AT91C_CAN_MB3_MDL.description="MailBox Data Low Register"
AT91C_CAN_MB3_MDL.helpkey="MailBox Data Low Register"
AT91C_CAN_MB3_MDL.access=memorymapped
AT91C_CAN_MB3_MDL.address=0xFFFD0274
AT91C_CAN_MB3_MDL.width=32
AT91C_CAN_MB3_MDL.byteEndian=little
AT91C_CAN_MB3_MDH.name="AT91C_CAN_MB3_MDH"
AT91C_CAN_MB3_MDH.description="MailBox Data High Register"
AT91C_CAN_MB3_MDH.helpkey="MailBox Data High Register"
AT91C_CAN_MB3_MDH.access=memorymapped
AT91C_CAN_MB3_MDH.address=0xFFFD0278
AT91C_CAN_MB3_MDH.width=32
AT91C_CAN_MB3_MDH.byteEndian=little
# ========== Register definition for CAN_MB4 peripheral ========== 
AT91C_CAN_MB4_MID.name="AT91C_CAN_MB4_MID"
AT91C_CAN_MB4_MID.description="MailBox ID Register"
AT91C_CAN_MB4_MID.helpkey="MailBox ID Register"
AT91C_CAN_MB4_MID.access=memorymapped
AT91C_CAN_MB4_MID.address=0xFFFD0288
AT91C_CAN_MB4_MID.width=32
AT91C_CAN_MB4_MID.byteEndian=little
AT91C_CAN_MB4_MMR.name="AT91C_CAN_MB4_MMR"
AT91C_CAN_MB4_MMR.description="MailBox Mode Register"
AT91C_CAN_MB4_MMR.helpkey="MailBox Mode Register"
AT91C_CAN_MB4_MMR.access=memorymapped
AT91C_CAN_MB4_MMR.address=0xFFFD0280
AT91C_CAN_MB4_MMR.width=32
AT91C_CAN_MB4_MMR.byteEndian=little
AT91C_CAN_MB4_MDH.name="AT91C_CAN_MB4_MDH"
AT91C_CAN_MB4_MDH.description="MailBox Data High Register"
AT91C_CAN_MB4_MDH.helpkey="MailBox Data High Register"
AT91C_CAN_MB4_MDH.access=memorymapped
AT91C_CAN_MB4_MDH.address=0xFFFD0298
AT91C_CAN_MB4_MDH.width=32
AT91C_CAN_MB4_MDH.byteEndian=little
AT91C_CAN_MB4_MFID.name="AT91C_CAN_MB4_MFID"
AT91C_CAN_MB4_MFID.description="MailBox Family ID Register"
AT91C_CAN_MB4_MFID.helpkey="MailBox Family ID Register"
AT91C_CAN_MB4_MFID.access=memorymapped
AT91C_CAN_MB4_MFID.address=0xFFFD028C
AT91C_CAN_MB4_MFID.width=32
AT91C_CAN_MB4_MFID.byteEndian=little
AT91C_CAN_MB4_MFID.permission.write=none
AT91C_CAN_MB4_MSR.name="AT91C_CAN_MB4_MSR"
AT91C_CAN_MB4_MSR.description="MailBox Status Register"
AT91C_CAN_MB4_MSR.helpkey="MailBox Status Register"
AT91C_CAN_MB4_MSR.access=memorymapped
AT91C_CAN_MB4_MSR.address=0xFFFD0290
AT91C_CAN_MB4_MSR.width=32
AT91C_CAN_MB4_MSR.byteEndian=little
AT91C_CAN_MB4_MSR.permission.write=none
AT91C_CAN_MB4_MCR.name="AT91C_CAN_MB4_MCR"
AT91C_CAN_MB4_MCR.description="MailBox Control Register"
AT91C_CAN_MB4_MCR.helpkey="MailBox Control Register"
AT91C_CAN_MB4_MCR.access=memorymapped
AT91C_CAN_MB4_MCR.address=0xFFFD029C
AT91C_CAN_MB4_MCR.width=32
AT91C_CAN_MB4_MCR.byteEndian=little
AT91C_CAN_MB4_MCR.type=enum
AT91C_CAN_MB4_MCR.enum.0.name=*** Write only ***
AT91C_CAN_MB4_MCR.enum.1.name=Error
AT91C_CAN_MB4_MDL.name="AT91C_CAN_MB4_MDL"
AT91C_CAN_MB4_MDL.description="MailBox Data Low Register"
AT91C_CAN_MB4_MDL.helpkey="MailBox Data Low Register"
AT91C_CAN_MB4_MDL.access=memorymapped
AT91C_CAN_MB4_MDL.address=0xFFFD0294
AT91C_CAN_MB4_MDL.width=32
AT91C_CAN_MB4_MDL.byteEndian=little
AT91C_CAN_MB4_MAM.name="AT91C_CAN_MB4_MAM"
AT91C_CAN_MB4_MAM.description="MailBox Acceptance Mask Register"
AT91C_CAN_MB4_MAM.helpkey="MailBox Acceptance Mask Register"
AT91C_CAN_MB4_MAM.access=memorymapped
AT91C_CAN_MB4_MAM.address=0xFFFD0284
AT91C_CAN_MB4_MAM.width=32
AT91C_CAN_MB4_MAM.byteEndian=little
# ========== Register definition for CAN_MB5 peripheral ========== 
AT91C_CAN_MB5_MSR.name="AT91C_CAN_MB5_MSR"
AT91C_CAN_MB5_MSR.description="MailBox Status Register"
AT91C_CAN_MB5_MSR.helpkey="MailBox Status Register"
AT91C_CAN_MB5_MSR.access=memorymapped
AT91C_CAN_MB5_MSR.address=0xFFFD02B0
AT91C_CAN_MB5_MSR.width=32
AT91C_CAN_MB5_MSR.byteEndian=little
AT91C_CAN_MB5_MSR.permission.write=none
AT91C_CAN_MB5_MCR.name="AT91C_CAN_MB5_MCR"
AT91C_CAN_MB5_MCR.description="MailBox Control Register"
AT91C_CAN_MB5_MCR.helpkey="MailBox Control Register"
AT91C_CAN_MB5_MCR.access=memorymapped
AT91C_CAN_MB5_MCR.address=0xFFFD02BC
AT91C_CAN_MB5_MCR.width=32
AT91C_CAN_MB5_MCR.byteEndian=little
AT91C_CAN_MB5_MCR.type=enum
AT91C_CAN_MB5_MCR.enum.0.name=*** Write only ***
AT91C_CAN_MB5_MCR.enum.1.name=Error
AT91C_CAN_MB5_MFID.name="AT91C_CAN_MB5_MFID"
AT91C_CAN_MB5_MFID.description="MailBox Family ID Register"
AT91C_CAN_MB5_MFID.helpkey="MailBox Family ID Register"
AT91C_CAN_MB5_MFID.access=memorymapped
AT91C_CAN_MB5_MFID.address=0xFFFD02AC
AT91C_CAN_MB5_MFID.width=32
AT91C_CAN_MB5_MFID.byteEndian=little
AT91C_CAN_MB5_MFID.permission.write=none
AT91C_CAN_MB5_MDH.name="AT91C_CAN_MB5_MDH"
AT91C_CAN_MB5_MDH.description="MailBox Data High Register"
AT91C_CAN_MB5_MDH.helpkey="MailBox Data High Register"
AT91C_CAN_MB5_MDH.access=memorymapped
AT91C_CAN_MB5_MDH.address=0xFFFD02B8
AT91C_CAN_MB5_MDH.width=32
AT91C_CAN_MB5_MDH.byteEndian=little
AT91C_CAN_MB5_MID.name="AT91C_CAN_MB5_MID"
AT91C_CAN_MB5_MID.description="MailBox ID Register"
AT91C_CAN_MB5_MID.helpkey="MailBox ID Register"
AT91C_CAN_MB5_MID.access=memorymapped
AT91C_CAN_MB5_MID.address=0xFFFD02A8
AT91C_CAN_MB5_MID.width=32
AT91C_CAN_MB5_MID.byteEndian=little
AT91C_CAN_MB5_MMR.name="AT91C_CAN_MB5_MMR"
AT91C_CAN_MB5_MMR.description="MailBox Mode Register"
AT91C_CAN_MB5_MMR.helpkey="MailBox Mode Register"
AT91C_CAN_MB5_MMR.access=memorymapped
AT91C_CAN_MB5_MMR.address=0xFFFD02A0
AT91C_CAN_MB5_MMR.width=32
AT91C_CAN_MB5_MMR.byteEndian=little
AT91C_CAN_MB5_MDL.name="AT91C_CAN_MB5_MDL"
AT91C_CAN_MB5_MDL.description="MailBox Data Low Register"
AT91C_CAN_MB5_MDL.helpkey="MailBox Data Low Register"
AT91C_CAN_MB5_MDL.access=memorymapped
AT91C_CAN_MB5_MDL.address=0xFFFD02B4
AT91C_CAN_MB5_MDL.width=32
AT91C_CAN_MB5_MDL.byteEndian=little
AT91C_CAN_MB5_MAM.name="AT91C_CAN_MB5_MAM"
AT91C_CAN_MB5_MAM.description="MailBox Acceptance Mask Register"
AT91C_CAN_MB5_MAM.helpkey="MailBox Acceptance Mask Register"
AT91C_CAN_MB5_MAM.access=memorymapped
AT91C_CAN_MB5_MAM.address=0xFFFD02A4
AT91C_CAN_MB5_MAM.width=32
AT91C_CAN_MB5_MAM.byteEndian=little
# ========== Register definition for CAN_MB6 peripheral ========== 
AT91C_CAN_MB6_MFID.name="AT91C_CAN_MB6_MFID"
AT91C_CAN_MB6_MFID.description="MailBox Family ID Register"
AT91C_CAN_MB6_MFID.helpkey="MailBox Family ID Register"
AT91C_CAN_MB6_MFID.access=memorymapped
AT91C_CAN_MB6_MFID.address=0xFFFD02CC
AT91C_CAN_MB6_MFID.width=32
AT91C_CAN_MB6_MFID.byteEndian=little
AT91C_CAN_MB6_MFID.permission.write=none
AT91C_CAN_MB6_MID.name="AT91C_CAN_MB6_MID"
AT91C_CAN_MB6_MID.description="MailBox ID Register"
AT91C_CAN_MB6_MID.helpkey="MailBox ID Register"
AT91C_CAN_MB6_MID.access=memorymapped
AT91C_CAN_MB6_MID.address=0xFFFD02C8
AT91C_CAN_MB6_MID.width=32
AT91C_CAN_MB6_MID.byteEndian=little
AT91C_CAN_MB6_MAM.name="AT91C_CAN_MB6_MAM"
AT91C_CAN_MB6_MAM.description="MailBox Acceptance Mask Register"
AT91C_CAN_MB6_MAM.helpkey="MailBox Acceptance Mask Register"
AT91C_CAN_MB6_MAM.access=memorymapped
AT91C_CAN_MB6_MAM.address=0xFFFD02C4
AT91C_CAN_MB6_MAM.width=32
AT91C_CAN_MB6_MAM.byteEndian=little
AT91C_CAN_MB6_MSR.name="AT91C_CAN_MB6_MSR"
AT91C_CAN_MB6_MSR.description="MailBox Status Register"
AT91C_CAN_MB6_MSR.helpkey="MailBox Status Register"
AT91C_CAN_MB6_MSR.access=memorymapped
AT91C_CAN_MB6_MSR.address=0xFFFD02D0
AT91C_CAN_MB6_MSR.width=32
AT91C_CAN_MB6_MSR.byteEndian=little
AT91C_CAN_MB6_MSR.permission.write=none
AT91C_CAN_MB6_MDL.name="AT91C_CAN_MB6_MDL"
AT91C_CAN_MB6_MDL.description="MailBox Data Low Register"
AT91C_CAN_MB6_MDL.helpkey="MailBox Data Low Register"
AT91C_CAN_MB6_MDL.access=memorymapped
AT91C_CAN_MB6_MDL.address=0xFFFD02D4
AT91C_CAN_MB6_MDL.width=32
AT91C_CAN_MB6_MDL.byteEndian=little
AT91C_CAN_MB6_MCR.name="AT91C_CAN_MB6_MCR"
AT91C_CAN_MB6_MCR.description="MailBox Control Register"
AT91C_CAN_MB6_MCR.helpkey="MailBox Control Register"
AT91C_CAN_MB6_MCR.access=memorymapped
AT91C_CAN_MB6_MCR.address=0xFFFD02DC
AT91C_CAN_MB6_MCR.width=32
AT91C_CAN_MB6_MCR.byteEndian=little
AT91C_CAN_MB6_MCR.type=enum
AT91C_CAN_MB6_MCR.enum.0.name=*** Write only ***
AT91C_CAN_MB6_MCR.enum.1.name=Error
AT91C_CAN_MB6_MDH.name="AT91C_CAN_MB6_MDH"
AT91C_CAN_MB6_MDH.description="MailBox Data High Register"
AT91C_CAN_MB6_MDH.helpkey="MailBox Data High Register"
AT91C_CAN_MB6_MDH.access=memorymapped
AT91C_CAN_MB6_MDH.address=0xFFFD02D8
AT91C_CAN_MB6_MDH.width=32
AT91C_CAN_MB6_MDH.byteEndian=little
AT91C_CAN_MB6_MMR.name="AT91C_CAN_MB6_MMR"
AT91C_CAN_MB6_MMR.description="MailBox Mode Register"
AT91C_CAN_MB6_MMR.helpkey="MailBox Mode Register"
AT91C_CAN_MB6_MMR.access=memorymapped
AT91C_CAN_MB6_MMR.address=0xFFFD02C0
AT91C_CAN_MB6_MMR.width=32
AT91C_CAN_MB6_MMR.byteEndian=little
# ========== Register definition for CAN_MB7 peripheral ========== 
AT91C_CAN_MB7_MCR.name="AT91C_CAN_MB7_MCR"
AT91C_CAN_MB7_MCR.description="MailBox Control Register"
AT91C_CAN_MB7_MCR.helpkey="MailBox Control Register"
AT91C_CAN_MB7_MCR.access=memorymapped
AT91C_CAN_MB7_MCR.address=0xFFFD02FC
AT91C_CAN_MB7_MCR.width=32
AT91C_CAN_MB7_MCR.byteEndian=little
AT91C_CAN_MB7_MCR.type=enum
AT91C_CAN_MB7_MCR.enum.0.name=*** Write only ***
AT91C_CAN_MB7_MCR.enum.1.name=Error
AT91C_CAN_MB7_MDH.name="AT91C_CAN_MB7_MDH"
AT91C_CAN_MB7_MDH.description="MailBox Data High Register"
AT91C_CAN_MB7_MDH.helpkey="MailBox Data High Register"
AT91C_CAN_MB7_MDH.access=memorymapped
AT91C_CAN_MB7_MDH.address=0xFFFD02F8
AT91C_CAN_MB7_MDH.width=32
AT91C_CAN_MB7_MDH.byteEndian=little
AT91C_CAN_MB7_MFID.name="AT91C_CAN_MB7_MFID"
AT91C_CAN_MB7_MFID.description="MailBox Family ID Register"
AT91C_CAN_MB7_MFID.helpkey="MailBox Family ID Register"
AT91C_CAN_MB7_MFID.access=memorymapped
AT91C_CAN_MB7_MFID.address=0xFFFD02EC
AT91C_CAN_MB7_MFID.width=32
AT91C_CAN_MB7_MFID.byteEndian=little
AT91C_CAN_MB7_MFID.permission.write=none
AT91C_CAN_MB7_MDL.name="AT91C_CAN_MB7_MDL"
AT91C_CAN_MB7_MDL.description="MailBox Data Low Register"
AT91C_CAN_MB7_MDL.helpkey="MailBox Data Low Register"
AT91C_CAN_MB7_MDL.access=memorymapped
AT91C_CAN_MB7_MDL.address=0xFFFD02F4
AT91C_CAN_MB7_MDL.width=32
AT91C_CAN_MB7_MDL.byteEndian=little
AT91C_CAN_MB7_MID.name="AT91C_CAN_MB7_MID"
AT91C_CAN_MB7_MID.description="MailBox ID Register"
AT91C_CAN_MB7_MID.helpkey="MailBox ID Register"
AT91C_CAN_MB7_MID.access=memorymapped
AT91C_CAN_MB7_MID.address=0xFFFD02E8
AT91C_CAN_MB7_MID.width=32
AT91C_CAN_MB7_MID.byteEndian=little
AT91C_CAN_MB7_MMR.name="AT91C_CAN_MB7_MMR"
AT91C_CAN_MB7_MMR.description="MailBox Mode Register"
AT91C_CAN_MB7_MMR.helpkey="MailBox Mode Register"
AT91C_CAN_MB7_MMR.access=memorymapped
AT91C_CAN_MB7_MMR.address=0xFFFD02E0
AT91C_CAN_MB7_MMR.width=32
AT91C_CAN_MB7_MMR.byteEndian=little
AT91C_CAN_MB7_MAM.name="AT91C_CAN_MB7_MAM"
AT91C_CAN_MB7_MAM.description="MailBox Acceptance Mask Register"
AT91C_CAN_MB7_MAM.helpkey="MailBox Acceptance Mask Register"
AT91C_CAN_MB7_MAM.access=memorymapped
AT91C_CAN_MB7_MAM.address=0xFFFD02E4
AT91C_CAN_MB7_MAM.width=32
AT91C_CAN_MB7_MAM.byteEndian=little
AT91C_CAN_MB7_MSR.name="AT91C_CAN_MB7_MSR"
AT91C_CAN_MB7_MSR.description="MailBox Status Register"
AT91C_CAN_MB7_MSR.helpkey="MailBox Status Register"
AT91C_CAN_MB7_MSR.access=memorymapped
AT91C_CAN_MB7_MSR.address=0xFFFD02F0
AT91C_CAN_MB7_MSR.width=32
AT91C_CAN_MB7_MSR.byteEndian=little
AT91C_CAN_MB7_MSR.permission.write=none
# ========== Register definition for CAN peripheral ========== 
AT91C_CAN_TCR.name="AT91C_CAN_TCR"
AT91C_CAN_TCR.description="Transfer Command Register"
AT91C_CAN_TCR.helpkey="Transfer Command Register"
AT91C_CAN_TCR.access=memorymapped
AT91C_CAN_TCR.address=0xFFFD0024
AT91C_CAN_TCR.width=32
AT91C_CAN_TCR.byteEndian=little
AT91C_CAN_TCR.type=enum
AT91C_CAN_TCR.enum.0.name=*** Write only ***
AT91C_CAN_TCR.enum.1.name=Error
AT91C_CAN_IMR.name="AT91C_CAN_IMR"
AT91C_CAN_IMR.description="Interrupt Mask Register"
AT91C_CAN_IMR.helpkey="Interrupt Mask Register"
AT91C_CAN_IMR.access=memorymapped
AT91C_CAN_IMR.address=0xFFFD000C
AT91C_CAN_IMR.width=32
AT91C_CAN_IMR.byteEndian=little
AT91C_CAN_IMR.permission.write=none
AT91C_CAN_IER.name="AT91C_CAN_IER"
AT91C_CAN_IER.description="Interrupt Enable Register"
AT91C_CAN_IER.helpkey="Interrupt Enable Register"
AT91C_CAN_IER.access=memorymapped
AT91C_CAN_IER.address=0xFFFD0004
AT91C_CAN_IER.width=32
AT91C_CAN_IER.byteEndian=little
AT91C_CAN_IER.type=enum
AT91C_CAN_IER.enum.0.name=*** Write only ***
AT91C_CAN_IER.enum.1.name=Error
AT91C_CAN_ECR.name="AT91C_CAN_ECR"
AT91C_CAN_ECR.description="Error Counter Register"
AT91C_CAN_ECR.helpkey="Error Counter Register"
AT91C_CAN_ECR.access=memorymapped
AT91C_CAN_ECR.address=0xFFFD0020
AT91C_CAN_ECR.width=32
AT91C_CAN_ECR.byteEndian=little
AT91C_CAN_ECR.permission.write=none
AT91C_CAN_TIMESTP.name="AT91C_CAN_TIMESTP"
AT91C_CAN_TIMESTP.description="Time Stamp Register"
AT91C_CAN_TIMESTP.helpkey="Time Stamp Register"
AT91C_CAN_TIMESTP.access=memorymapped
AT91C_CAN_TIMESTP.address=0xFFFD001C
AT91C_CAN_TIMESTP.width=32
AT91C_CAN_TIMESTP.byteEndian=little
AT91C_CAN_TIMESTP.permission.write=none
AT91C_CAN_MR.name="AT91C_CAN_MR"
AT91C_CAN_MR.description="Mode Register"
AT91C_CAN_MR.helpkey="Mode Register"
AT91C_CAN_MR.access=memorymapped
AT91C_CAN_MR.address=0xFFFD0000
AT91C_CAN_MR.width=32
AT91C_CAN_MR.byteEndian=little
AT91C_CAN_IDR.name="AT91C_CAN_IDR"
AT91C_CAN_IDR.description="Interrupt Disable Register"
AT91C_CAN_IDR.helpkey="Interrupt Disable Register"
AT91C_CAN_IDR.access=memorymapped
AT91C_CAN_IDR.address=0xFFFD0008
AT91C_CAN_IDR.width=32
AT91C_CAN_IDR.byteEndian=little
AT91C_CAN_IDR.type=enum
AT91C_CAN_IDR.enum.0.name=*** Write only ***
AT91C_CAN_IDR.enum.1.name=Error
AT91C_CAN_ACR.name="AT91C_CAN_ACR"
AT91C_CAN_ACR.description="Abort Command Register"
AT91C_CAN_ACR.helpkey="Abort Command Register"
AT91C_CAN_ACR.access=memorymapped
AT91C_CAN_ACR.address=0xFFFD0028
AT91C_CAN_ACR.width=32
AT91C_CAN_ACR.byteEndian=little
AT91C_CAN_ACR.type=enum
AT91C_CAN_ACR.enum.0.name=*** Write only ***
AT91C_CAN_ACR.enum.1.name=Error
AT91C_CAN_TIM.name="AT91C_CAN_TIM"
AT91C_CAN_TIM.description="Timer Register"
AT91C_CAN_TIM.helpkey="Timer Register"
AT91C_CAN_TIM.access=memorymapped
AT91C_CAN_TIM.address=0xFFFD0018
AT91C_CAN_TIM.width=32
AT91C_CAN_TIM.byteEndian=little
AT91C_CAN_TIM.permission.write=none
AT91C_CAN_SR.name="AT91C_CAN_SR"
AT91C_CAN_SR.description="Status Register"
AT91C_CAN_SR.helpkey="Status Register"
AT91C_CAN_SR.access=memorymapped
AT91C_CAN_SR.address=0xFFFD0010
AT91C_CAN_SR.width=32
AT91C_CAN_SR.byteEndian=little
AT91C_CAN_SR.permission.write=none
AT91C_CAN_BR.name="AT91C_CAN_BR"
AT91C_CAN_BR.description="Baudrate Register"
AT91C_CAN_BR.helpkey="Baudrate Register"
AT91C_CAN_BR.access=memorymapped
AT91C_CAN_BR.address=0xFFFD0014
AT91C_CAN_BR.width=32
AT91C_CAN_BR.byteEndian=little
AT91C_CAN_VR.name="AT91C_CAN_VR"
AT91C_CAN_VR.description="Version Register"
AT91C_CAN_VR.helpkey="Version Register"
AT91C_CAN_VR.access=memorymapped
AT91C_CAN_VR.address=0xFFFD00FC
AT91C_CAN_VR.width=32
AT91C_CAN_VR.byteEndian=little
AT91C_CAN_VR.permission.write=none
# ========== Register definition for EMAC peripheral ========== 
AT91C_EMAC_ISR.name="AT91C_EMAC_ISR"
AT91C_EMAC_ISR.description="Interrupt Status Register"
AT91C_EMAC_ISR.helpkey="Interrupt Status Register"
AT91C_EMAC_ISR.access=memorymapped
AT91C_EMAC_ISR.address=0xFFFDC024
AT91C_EMAC_ISR.width=32
AT91C_EMAC_ISR.byteEndian=little
AT91C_EMAC_SA4H.name="AT91C_EMAC_SA4H"
AT91C_EMAC_SA4H.description="Specific Address 4 Top, Last 2 bytes"
AT91C_EMAC_SA4H.helpkey="Specific Address 4 Top, Last 2 bytes"
AT91C_EMAC_SA4H.access=memorymapped
AT91C_EMAC_SA4H.address=0xFFFDC0B4
AT91C_EMAC_SA4H.width=32
AT91C_EMAC_SA4H.byteEndian=little
AT91C_EMAC_SA1L.name="AT91C_EMAC_SA1L"
AT91C_EMAC_SA1L.description="Specific Address 1 Bottom, First 4 bytes"
AT91C_EMAC_SA1L.helpkey="Specific Address 1 Bottom, First 4 bytes"
AT91C_EMAC_SA1L.access=memorymapped
AT91C_EMAC_SA1L.address=0xFFFDC098
AT91C_EMAC_SA1L.width=32
AT91C_EMAC_SA1L.byteEndian=little
AT91C_EMAC_ELE.name="AT91C_EMAC_ELE"
AT91C_EMAC_ELE.description="Excessive Length Errors Register"
AT91C_EMAC_ELE.helpkey="Excessive Length Errors Register"
AT91C_EMAC_ELE.access=memorymapped
AT91C_EMAC_ELE.address=0xFFFDC078
AT91C_EMAC_ELE.width=32
AT91C_EMAC_ELE.byteEndian=little
AT91C_EMAC_LCOL.name="AT91C_EMAC_LCOL"
AT91C_EMAC_LCOL.description="Late Collision Register"
AT91C_EMAC_LCOL.helpkey="Late Collision Register"
AT91C_EMAC_LCOL.access=memorymapped
AT91C_EMAC_LCOL.address=0xFFFDC05C
AT91C_EMAC_LCOL.width=32
AT91C_EMAC_LCOL.byteEndian=little
AT91C_EMAC_RLE.name="AT91C_EMAC_RLE"
AT91C_EMAC_RLE.description="Receive Length Field Mismatch Register"
AT91C_EMAC_RLE.helpkey="Receive Length Field Mismatch Register"
AT91C_EMAC_RLE.access=memorymapped
AT91C_EMAC_RLE.address=0xFFFDC088
AT91C_EMAC_RLE.width=32
AT91C_EMAC_RLE.byteEndian=little
AT91C_EMAC_WOL.name="AT91C_EMAC_WOL"
AT91C_EMAC_WOL.description="Wake On LAN Register"
AT91C_EMAC_WOL.helpkey="Wake On LAN Register"
AT91C_EMAC_WOL.access=memorymapped
AT91C_EMAC_WOL.address=0xFFFDC0C4
AT91C_EMAC_WOL.width=32
AT91C_EMAC_WOL.byteEndian=little
AT91C_EMAC_DTF.name="AT91C_EMAC_DTF"
AT91C_EMAC_DTF.description="Deferred Transmission Frame Register"
AT91C_EMAC_DTF.helpkey="Deferred Transmission Frame Register"
AT91C_EMAC_DTF.access=memorymapped
AT91C_EMAC_DTF.address=0xFFFDC058
AT91C_EMAC_DTF.width=32
AT91C_EMAC_DTF.byteEndian=little
AT91C_EMAC_TUND.name="AT91C_EMAC_TUND"
AT91C_EMAC_TUND.description="Transmit Underrun Error Register"
AT91C_EMAC_TUND.helpkey="Transmit Underrun Error Register"
AT91C_EMAC_TUND.access=memorymapped
AT91C_EMAC_TUND.address=0xFFFDC064
AT91C_EMAC_TUND.width=32
AT91C_EMAC_TUND.byteEndian=little
AT91C_EMAC_NCR.name="AT91C_EMAC_NCR"
AT91C_EMAC_NCR.description="Network Control Register"
AT91C_EMAC_NCR.helpkey="Network Control Register"
AT91C_EMAC_NCR.access=memorymapped
AT91C_EMAC_NCR.address=0xFFFDC000
AT91C_EMAC_NCR.width=32
AT91C_EMAC_NCR.byteEndian=little
AT91C_EMAC_SA4L.name="AT91C_EMAC_SA4L"
AT91C_EMAC_SA4L.description="Specific Address 4 Bottom, First 4 bytes"
AT91C_EMAC_SA4L.helpkey="Specific Address 4 Bottom, First 4 bytes"
AT91C_EMAC_SA4L.access=memorymapped
AT91C_EMAC_SA4L.address=0xFFFDC0B0
AT91C_EMAC_SA4L.width=32
AT91C_EMAC_SA4L.byteEndian=little
AT91C_EMAC_RSR.name="AT91C_EMAC_RSR"
AT91C_EMAC_RSR.description="Receive Status Register"
AT91C_EMAC_RSR.helpkey="Receive Status Register"
AT91C_EMAC_RSR.access=memorymapped
AT91C_EMAC_RSR.address=0xFFFDC020
AT91C_EMAC_RSR.width=32
AT91C_EMAC_RSR.byteEndian=little
AT91C_EMAC_SA3L.name="AT91C_EMAC_SA3L"
AT91C_EMAC_SA3L.description="Specific Address 3 Bottom, First 4 bytes"
AT91C_EMAC_SA3L.helpkey="Specific Address 3 Bottom, First 4 bytes"
AT91C_EMAC_SA3L.access=memorymapped
AT91C_EMAC_SA3L.address=0xFFFDC0A8
AT91C_EMAC_SA3L.width=32
AT91C_EMAC_SA3L.byteEndian=little
AT91C_EMAC_TSR.name="AT91C_EMAC_TSR"
AT91C_EMAC_TSR.description="Transmit Status Register"
AT91C_EMAC_TSR.helpkey="Transmit Status Register"
AT91C_EMAC_TSR.access=memorymapped
AT91C_EMAC_TSR.address=0xFFFDC014
AT91C_EMAC_TSR.width=32
AT91C_EMAC_TSR.byteEndian=little
AT91C_EMAC_IDR.name="AT91C_EMAC_IDR"
AT91C_EMAC_IDR.description="Interrupt Disable Register"
AT91C_EMAC_IDR.helpkey="Interrupt Disable Register"
AT91C_EMAC_IDR.access=memorymapped
AT91C_EMAC_IDR.address=0xFFFDC02C
AT91C_EMAC_IDR.width=32
AT91C_EMAC_IDR.byteEndian=little
AT91C_EMAC_IDR.type=enum
AT91C_EMAC_IDR.enum.0.name=*** Write only ***
AT91C_EMAC_IDR.enum.1.name=Error
AT91C_EMAC_RSE.name="AT91C_EMAC_RSE"
AT91C_EMAC_RSE.description="Receive Symbol Errors Register"
AT91C_EMAC_RSE.helpkey="Receive Symbol Errors Register"
AT91C_EMAC_RSE.access=memorymapped
AT91C_EMAC_RSE.address=0xFFFDC074
AT91C_EMAC_RSE.width=32
AT91C_EMAC_RSE.byteEndian=little
AT91C_EMAC_ECOL.name="AT91C_EMAC_ECOL"
AT91C_EMAC_ECOL.description="Excessive Collision Register"
AT91C_EMAC_ECOL.helpkey="Excessive Collision Register"
AT91C_EMAC_ECOL.access=memorymapped
AT91C_EMAC_ECOL.address=0xFFFDC060
AT91C_EMAC_ECOL.width=32
AT91C_EMAC_ECOL.byteEndian=little
AT91C_EMAC_TID.name="AT91C_EMAC_TID"
AT91C_EMAC_TID.description="Type ID Checking Register"
AT91C_EMAC_TID.helpkey="Type ID Checking Register"
AT91C_EMAC_TID.access=memorymapped
AT91C_EMAC_TID.address=0xFFFDC0B8
AT91C_EMAC_TID.width=32
AT91C_EMAC_TID.byteEndian=little
AT91C_EMAC_HRB.name="AT91C_EMAC_HRB"
AT91C_EMAC_HRB.description="Hash Address Bottom[31:0]"
AT91C_EMAC_HRB.helpkey="Hash Address Bottom[31:0]"
AT91C_EMAC_HRB.access=memorymapped
AT91C_EMAC_HRB.address=0xFFFDC090
AT91C_EMAC_HRB.width=32
AT91C_EMAC_HRB.byteEndian=little
AT91C_EMAC_TBQP.name="AT91C_EMAC_TBQP"
AT91C_EMAC_TBQP.description="Transmit Buffer Queue Pointer"
AT91C_EMAC_TBQP.helpkey="Transmit Buffer Queue Pointer"
AT91C_EMAC_TBQP.access=memorymapped
AT91C_EMAC_TBQP.address=0xFFFDC01C
AT91C_EMAC_TBQP.width=32
AT91C_EMAC_TBQP.byteEndian=little
AT91C_EMAC_USRIO.name="AT91C_EMAC_USRIO"
AT91C_EMAC_USRIO.description="USER Input/Output Register"
AT91C_EMAC_USRIO.helpkey="USER Input/Output Register"
AT91C_EMAC_USRIO.access=memorymapped
AT91C_EMAC_USRIO.address=0xFFFDC0C0
AT91C_EMAC_USRIO.width=32
AT91C_EMAC_USRIO.byteEndian=little
AT91C_EMAC_PTR.name="AT91C_EMAC_PTR"
AT91C_EMAC_PTR.description="Pause Time Register"
AT91C_EMAC_PTR.helpkey="Pause Time Register"
AT91C_EMAC_PTR.access=memorymapped
AT91C_EMAC_PTR.address=0xFFFDC038
AT91C_EMAC_PTR.width=32
AT91C_EMAC_PTR.byteEndian=little
AT91C_EMAC_SA2H.name="AT91C_EMAC_SA2H"
AT91C_EMAC_SA2H.description="Specific Address 2 Top, Last 2 bytes"
AT91C_EMAC_SA2H.helpkey="Specific Address 2 Top, Last 2 bytes"
AT91C_EMAC_SA2H.access=memorymapped
AT91C_EMAC_SA2H.address=0xFFFDC0A4
AT91C_EMAC_SA2H.width=32
AT91C_EMAC_SA2H.byteEndian=little
AT91C_EMAC_ROV.name="AT91C_EMAC_ROV"
AT91C_EMAC_ROV.description="Receive Overrun Errors Register"
AT91C_EMAC_ROV.helpkey="Receive Overrun Errors Register"
AT91C_EMAC_ROV.access=memorymapped
AT91C_EMAC_ROV.address=0xFFFDC070
AT91C_EMAC_ROV.width=32
AT91C_EMAC_ROV.byteEndian=little
AT91C_EMAC_ALE.name="AT91C_EMAC_ALE"
AT91C_EMAC_ALE.description="Alignment Error Register"
AT91C_EMAC_ALE.helpkey="Alignment Error Register"
AT91C_EMAC_ALE.access=memorymapped
AT91C_EMAC_ALE.address=0xFFFDC054
AT91C_EMAC_ALE.width=32
AT91C_EMAC_ALE.byteEndian=little
AT91C_EMAC_RJA.name="AT91C_EMAC_RJA"
AT91C_EMAC_RJA.description="Receive Jabbers Register"
AT91C_EMAC_RJA.helpkey="Receive Jabbers Register"
AT91C_EMAC_RJA.access=memorymapped
AT91C_EMAC_RJA.address=0xFFFDC07C
AT91C_EMAC_RJA.width=32
AT91C_EMAC_RJA.byteEndian=little
AT91C_EMAC_RBQP.name="AT91C_EMAC_RBQP"
AT91C_EMAC_RBQP.description="Receive Buffer Queue Pointer"
AT91C_EMAC_RBQP.helpkey="Receive Buffer Queue Pointer"
AT91C_EMAC_RBQP.access=memorymapped
AT91C_EMAC_RBQP.address=0xFFFDC018
AT91C_EMAC_RBQP.width=32
AT91C_EMAC_RBQP.byteEndian=little
AT91C_EMAC_TPF.name="AT91C_EMAC_TPF"
AT91C_EMAC_TPF.description="Transmitted Pause Frames Register"
AT91C_EMAC_TPF.helpkey="Transmitted Pause Frames Register"
AT91C_EMAC_TPF.access=memorymapped
AT91C_EMAC_TPF.address=0xFFFDC08C
AT91C_EMAC_TPF.width=32
AT91C_EMAC_TPF.byteEndian=little
AT91C_EMAC_NCFGR.name="AT91C_EMAC_NCFGR"
AT91C_EMAC_NCFGR.description="Network Configuration Register"
AT91C_EMAC_NCFGR.helpkey="Network Configuration Register"
AT91C_EMAC_NCFGR.access=memorymapped
AT91C_EMAC_NCFGR.address=0xFFFDC004
AT91C_EMAC_NCFGR.width=32
AT91C_EMAC_NCFGR.byteEndian=little
AT91C_EMAC_HRT.name="AT91C_EMAC_HRT"
AT91C_EMAC_HRT.description="Hash Address Top[63:32]"
AT91C_EMAC_HRT.helpkey="Hash Address Top[63:32]"
AT91C_EMAC_HRT.access=memorymapped
AT91C_EMAC_HRT.address=0xFFFDC094
AT91C_EMAC_HRT.width=32
AT91C_EMAC_HRT.byteEndian=little
AT91C_EMAC_USF.name="AT91C_EMAC_USF"
AT91C_EMAC_USF.description="Undersize Frames Register"
AT91C_EMAC_USF.helpkey="Undersize Frames Register"
AT91C_EMAC_USF.access=memorymapped
AT91C_EMAC_USF.address=0xFFFDC080
AT91C_EMAC_USF.width=32
AT91C_EMAC_USF.byteEndian=little
AT91C_EMAC_FCSE.name="AT91C_EMAC_FCSE"
AT91C_EMAC_FCSE.description="Frame Check Sequence Error Register"
AT91C_EMAC_FCSE.helpkey="Frame Check Sequence Error Register"
AT91C_EMAC_FCSE.access=memorymapped
AT91C_EMAC_FCSE.address=0xFFFDC050
AT91C_EMAC_FCSE.width=32
AT91C_EMAC_FCSE.byteEndian=little
AT91C_EMAC_TPQ.name="AT91C_EMAC_TPQ"
AT91C_EMAC_TPQ.description="Transmit Pause Quantum Register"
AT91C_EMAC_TPQ.helpkey="Transmit Pause Quantum Register"
AT91C_EMAC_TPQ.access=memorymapped
AT91C_EMAC_TPQ.address=0xFFFDC0BC
AT91C_EMAC_TPQ.width=32
AT91C_EMAC_TPQ.byteEndian=little
AT91C_EMAC_MAN.name="AT91C_EMAC_MAN"
AT91C_EMAC_MAN.description="PHY Maintenance Register"
AT91C_EMAC_MAN.helpkey="PHY Maintenance Register"
AT91C_EMAC_MAN.access=memorymapped
AT91C_EMAC_MAN.address=0xFFFDC034
AT91C_EMAC_MAN.width=32
AT91C_EMAC_MAN.byteEndian=little
AT91C_EMAC_FTO.name="AT91C_EMAC_FTO"
AT91C_EMAC_FTO.description="Frames Transmitted OK Register"
AT91C_EMAC_FTO.helpkey="Frames Transmitted OK Register"
AT91C_EMAC_FTO.access=memorymapped
AT91C_EMAC_FTO.address=0xFFFDC040
AT91C_EMAC_FTO.width=32
AT91C_EMAC_FTO.byteEndian=little
AT91C_EMAC_REV.name="AT91C_EMAC_REV"
AT91C_EMAC_REV.description="Revision Register"
AT91C_EMAC_REV.helpkey="Revision Register"
AT91C_EMAC_REV.access=memorymapped
AT91C_EMAC_REV.address=0xFFFDC0FC
AT91C_EMAC_REV.width=32
AT91C_EMAC_REV.byteEndian=little
AT91C_EMAC_REV.permission.write=none
AT91C_EMAC_IMR.name="AT91C_EMAC_IMR"
AT91C_EMAC_IMR.description="Interrupt Mask Register"
AT91C_EMAC_IMR.helpkey="Interrupt Mask Register"
AT91C_EMAC_IMR.access=memorymapped
AT91C_EMAC_IMR.address=0xFFFDC030
AT91C_EMAC_IMR.width=32
AT91C_EMAC_IMR.byteEndian=little
AT91C_EMAC_IMR.permission.write=none
AT91C_EMAC_SCF.name="AT91C_EMAC_SCF"
AT91C_EMAC_SCF.description="Single Collision Frame Register"
AT91C_EMAC_SCF.helpkey="Single Collision Frame Register"
AT91C_EMAC_SCF.access=memorymapped
AT91C_EMAC_SCF.address=0xFFFDC044
AT91C_EMAC_SCF.width=32
AT91C_EMAC_SCF.byteEndian=little
AT91C_EMAC_PFR.name="AT91C_EMAC_PFR"
AT91C_EMAC_PFR.description="Pause Frames received Register"
AT91C_EMAC_PFR.helpkey="Pause Frames received Register"
AT91C_EMAC_PFR.access=memorymapped
AT91C_EMAC_PFR.address=0xFFFDC03C
AT91C_EMAC_PFR.width=32
AT91C_EMAC_PFR.byteEndian=little
AT91C_EMAC_MCF.name="AT91C_EMAC_MCF"
AT91C_EMAC_MCF.description="Multiple Collision Frame Register"
AT91C_EMAC_MCF.helpkey="Multiple Collision Frame Register"
AT91C_EMAC_MCF.access=memorymapped
AT91C_EMAC_MCF.address=0xFFFDC048
AT91C_EMAC_MCF.width=32
AT91C_EMAC_MCF.byteEndian=little
AT91C_EMAC_NSR.name="AT91C_EMAC_NSR"
AT91C_EMAC_NSR.description="Network Status Register"
AT91C_EMAC_NSR.helpkey="Network Status Register"
AT91C_EMAC_NSR.access=memorymapped
AT91C_EMAC_NSR.address=0xFFFDC008
AT91C_EMAC_NSR.width=32
AT91C_EMAC_NSR.byteEndian=little
AT91C_EMAC_NSR.permission.write=none
AT91C_EMAC_SA2L.name="AT91C_EMAC_SA2L"
AT91C_EMAC_SA2L.description="Specific Address 2 Bottom, First 4 bytes"
AT91C_EMAC_SA2L.helpkey="Specific Address 2 Bottom, First 4 bytes"
AT91C_EMAC_SA2L.access=memorymapped
AT91C_EMAC_SA2L.address=0xFFFDC0A0
AT91C_EMAC_SA2L.width=32
AT91C_EMAC_SA2L.byteEndian=little
AT91C_EMAC_FRO.name="AT91C_EMAC_FRO"
AT91C_EMAC_FRO.description="Frames Received OK Register"
AT91C_EMAC_FRO.helpkey="Frames Received OK Register"
AT91C_EMAC_FRO.access=memorymapped
AT91C_EMAC_FRO.address=0xFFFDC04C
AT91C_EMAC_FRO.width=32
AT91C_EMAC_FRO.byteEndian=little
AT91C_EMAC_IER.name="AT91C_EMAC_IER"
AT91C_EMAC_IER.description="Interrupt Enable Register"
AT91C_EMAC_IER.helpkey="Interrupt Enable Register"
AT91C_EMAC_IER.access=memorymapped
AT91C_EMAC_IER.address=0xFFFDC028
AT91C_EMAC_IER.width=32
AT91C_EMAC_IER.byteEndian=little
AT91C_EMAC_IER.type=enum
AT91C_EMAC_IER.enum.0.name=*** Write only ***
AT91C_EMAC_IER.enum.1.name=Error
AT91C_EMAC_SA1H.name="AT91C_EMAC_SA1H"
AT91C_EMAC_SA1H.description="Specific Address 1 Top, Last 2 bytes"
AT91C_EMAC_SA1H.helpkey="Specific Address 1 Top, Last 2 bytes"
AT91C_EMAC_SA1H.access=memorymapped
AT91C_EMAC_SA1H.address=0xFFFDC09C
AT91C_EMAC_SA1H.width=32
AT91C_EMAC_SA1H.byteEndian=little
AT91C_EMAC_CSE.name="AT91C_EMAC_CSE"
AT91C_EMAC_CSE.description="Carrier Sense Error Register"
AT91C_EMAC_CSE.helpkey="Carrier Sense Error Register"
AT91C_EMAC_CSE.access=memorymapped
AT91C_EMAC_CSE.address=0xFFFDC068
AT91C_EMAC_CSE.width=32
AT91C_EMAC_CSE.byteEndian=little
AT91C_EMAC_SA3H.name="AT91C_EMAC_SA3H"
AT91C_EMAC_SA3H.description="Specific Address 3 Top, Last 2 bytes"
AT91C_EMAC_SA3H.helpkey="Specific Address 3 Top, Last 2 bytes"
AT91C_EMAC_SA3H.access=memorymapped
AT91C_EMAC_SA3H.address=0xFFFDC0AC
AT91C_EMAC_SA3H.width=32
AT91C_EMAC_SA3H.byteEndian=little
AT91C_EMAC_RRE.name="AT91C_EMAC_RRE"
AT91C_EMAC_RRE.description="Receive Ressource Error Register"
AT91C_EMAC_RRE.helpkey="Receive Ressource Error Register"
AT91C_EMAC_RRE.access=memorymapped
AT91C_EMAC_RRE.address=0xFFFDC06C
AT91C_EMAC_RRE.width=32
AT91C_EMAC_RRE.byteEndian=little
AT91C_EMAC_STE.name="AT91C_EMAC_STE"
AT91C_EMAC_STE.description="SQE Test Error Register"
AT91C_EMAC_STE.helpkey="SQE Test Error Register"
AT91C_EMAC_STE.access=memorymapped
AT91C_EMAC_STE.address=0xFFFDC084
AT91C_EMAC_STE.width=32
AT91C_EMAC_STE.byteEndian=little
# ========== Register definition for PDC_ADC peripheral ========== 
AT91C_ADC_PTSR.name="AT91C_ADC_PTSR"
AT91C_ADC_PTSR.description="PDC Transfer Status Register"
AT91C_ADC_PTSR.helpkey="PDC Transfer Status Register"
AT91C_ADC_PTSR.access=memorymapped
AT91C_ADC_PTSR.address=0xFFFD8124
AT91C_ADC_PTSR.width=32
AT91C_ADC_PTSR.byteEndian=little
AT91C_ADC_PTSR.permission.write=none
AT91C_ADC_PTCR.name="AT91C_ADC_PTCR"
AT91C_ADC_PTCR.description="PDC Transfer Control Register"
AT91C_ADC_PTCR.helpkey="PDC Transfer Control Register"
AT91C_ADC_PTCR.access=memorymapped
AT91C_ADC_PTCR.address=0xFFFD8120
AT91C_ADC_PTCR.width=32
AT91C_ADC_PTCR.byteEndian=little
AT91C_ADC_PTCR.type=enum
AT91C_ADC_PTCR.enum.0.name=*** Write only ***
AT91C_ADC_PTCR.enum.1.name=Error
AT91C_ADC_TNPR.name="AT91C_ADC_TNPR"
AT91C_ADC_TNPR.description="Transmit Next Pointer Register"
AT91C_ADC_TNPR.helpkey="Transmit Next Pointer Register"
AT91C_ADC_TNPR.access=memorymapped
AT91C_ADC_TNPR.address=0xFFFD8118
AT91C_ADC_TNPR.width=32
AT91C_ADC_TNPR.byteEndian=little
AT91C_ADC_TNCR.name="AT91C_ADC_TNCR"
AT91C_ADC_TNCR.description="Transmit Next Counter Register"
AT91C_ADC_TNCR.helpkey="Transmit Next Counter Register"
AT91C_ADC_TNCR.access=memorymapped
AT91C_ADC_TNCR.address=0xFFFD811C
AT91C_ADC_TNCR.width=32
AT91C_ADC_TNCR.byteEndian=little
AT91C_ADC_RNPR.name="AT91C_ADC_RNPR"
AT91C_ADC_RNPR.description="Receive Next Pointer Register"
AT91C_ADC_RNPR.helpkey="Receive Next Pointer Register"
AT91C_ADC_RNPR.access=memorymapped
AT91C_ADC_RNPR.address=0xFFFD8110
AT91C_ADC_RNPR.width=32
AT91C_ADC_RNPR.byteEndian=little
AT91C_ADC_RNCR.name="AT91C_ADC_RNCR"
AT91C_ADC_RNCR.description="Receive Next Counter Register"
AT91C_ADC_RNCR.helpkey="Receive Next Counter Register"
AT91C_ADC_RNCR.access=memorymapped
AT91C_ADC_RNCR.address=0xFFFD8114
AT91C_ADC_RNCR.width=32
AT91C_ADC_RNCR.byteEndian=little
AT91C_ADC_RPR.name="AT91C_ADC_RPR"
AT91C_ADC_RPR.description="Receive Pointer Register"
AT91C_ADC_RPR.helpkey="Receive Pointer Register"
AT91C_ADC_RPR.access=memorymapped
AT91C_ADC_RPR.address=0xFFFD8100
AT91C_ADC_RPR.width=32
AT91C_ADC_RPR.byteEndian=little
AT91C_ADC_TCR.name="AT91C_ADC_TCR"
AT91C_ADC_TCR.description="Transmit Counter Register"
AT91C_ADC_TCR.helpkey="Transmit Counter Register"
AT91C_ADC_TCR.access=memorymapped
AT91C_ADC_TCR.address=0xFFFD810C
AT91C_ADC_TCR.width=32
AT91C_ADC_TCR.byteEndian=little
AT91C_ADC_TPR.name="AT91C_ADC_TPR"
AT91C_ADC_TPR.description="Transmit Pointer Register"
AT91C_ADC_TPR.helpkey="Transmit Pointer Register"
AT91C_ADC_TPR.access=memorymapped
AT91C_ADC_TPR.address=0xFFFD8108
AT91C_ADC_TPR.width=32
AT91C_ADC_TPR.byteEndian=little
AT91C_ADC_RCR.name="AT91C_ADC_RCR"
AT91C_ADC_RCR.description="Receive Counter Register"
AT91C_ADC_RCR.helpkey="Receive Counter Register"
AT91C_ADC_RCR.access=memorymapped
AT91C_ADC_RCR.address=0xFFFD8104
AT91C_ADC_RCR.width=32
AT91C_ADC_RCR.byteEndian=little
# ========== Register definition for ADC peripheral ========== 
AT91C_ADC_CDR2.name="AT91C_ADC_CDR2"
AT91C_ADC_CDR2.description="ADC Channel Data Register 2"
AT91C_ADC_CDR2.helpkey="ADC Channel Data Register 2"
AT91C_ADC_CDR2.access=memorymapped
AT91C_ADC_CDR2.address=0xFFFD8038
AT91C_ADC_CDR2.width=32
AT91C_ADC_CDR2.byteEndian=little
AT91C_ADC_CDR2.permission.write=none
AT91C_ADC_CDR3.name="AT91C_ADC_CDR3"
AT91C_ADC_CDR3.description="ADC Channel Data Register 3"
AT91C_ADC_CDR3.helpkey="ADC Channel Data Register 3"
AT91C_ADC_CDR3.access=memorymapped
AT91C_ADC_CDR3.address=0xFFFD803C
AT91C_ADC_CDR3.width=32
AT91C_ADC_CDR3.byteEndian=little
AT91C_ADC_CDR3.permission.write=none
AT91C_ADC_CDR0.name="AT91C_ADC_CDR0"
AT91C_ADC_CDR0.description="ADC Channel Data Register 0"
AT91C_ADC_CDR0.helpkey="ADC Channel Data Register 0"
AT91C_ADC_CDR0.access=memorymapped
AT91C_ADC_CDR0.address=0xFFFD8030
AT91C_ADC_CDR0.width=32
AT91C_ADC_CDR0.byteEndian=little
AT91C_ADC_CDR0.permission.write=none
AT91C_ADC_CDR5.name="AT91C_ADC_CDR5"
AT91C_ADC_CDR5.description="ADC Channel Data Register 5"
AT91C_ADC_CDR5.helpkey="ADC Channel Data Register 5"
AT91C_ADC_CDR5.access=memorymapped
AT91C_ADC_CDR5.address=0xFFFD8044
AT91C_ADC_CDR5.width=32
AT91C_ADC_CDR5.byteEndian=little
AT91C_ADC_CDR5.permission.write=none
AT91C_ADC_CHDR.name="AT91C_ADC_CHDR"
AT91C_ADC_CHDR.description="ADC Channel Disable Register"
AT91C_ADC_CHDR.helpkey="ADC Channel Disable Register"
AT91C_ADC_CHDR.access=memorymapped
AT91C_ADC_CHDR.address=0xFFFD8014
AT91C_ADC_CHDR.width=32
AT91C_ADC_CHDR.byteEndian=little
AT91C_ADC_CHDR.type=enum
AT91C_ADC_CHDR.enum.0.name=*** Write only ***
AT91C_ADC_CHDR.enum.1.name=Error
AT91C_ADC_SR.name="AT91C_ADC_SR"
AT91C_ADC_SR.description="ADC Status Register"
AT91C_ADC_SR.helpkey="ADC Status Register"
AT91C_ADC_SR.access=memorymapped
AT91C_ADC_SR.address=0xFFFD801C
AT91C_ADC_SR.width=32
AT91C_ADC_SR.byteEndian=little
AT91C_ADC_SR.permission.write=none
AT91C_ADC_CDR4.name="AT91C_ADC_CDR4"
AT91C_ADC_CDR4.description="ADC Channel Data Register 4"
AT91C_ADC_CDR4.helpkey="ADC Channel Data Register 4"
AT91C_ADC_CDR4.access=memorymapped
AT91C_ADC_CDR4.address=0xFFFD8040
AT91C_ADC_CDR4.width=32
AT91C_ADC_CDR4.byteEndian=little
AT91C_ADC_CDR4.permission.write=none
AT91C_ADC_CDR1.name="AT91C_ADC_CDR1"
AT91C_ADC_CDR1.description="ADC Channel Data Register 1"
AT91C_ADC_CDR1.helpkey="ADC Channel Data Register 1"
AT91C_ADC_CDR1.access=memorymapped
AT91C_ADC_CDR1.address=0xFFFD8034
AT91C_ADC_CDR1.width=32
AT91C_ADC_CDR1.byteEndian=little
AT91C_ADC_CDR1.permission.write=none
AT91C_ADC_LCDR.name="AT91C_ADC_LCDR"
AT91C_ADC_LCDR.description="ADC Last Converted Data Register"
AT91C_ADC_LCDR.helpkey="ADC Last Converted Data Register"
AT91C_ADC_LCDR.access=memorymapped
AT91C_ADC_LCDR.address=0xFFFD8020
AT91C_ADC_LCDR.width=32
AT91C_ADC_LCDR.byteEndian=little
AT91C_ADC_LCDR.permission.write=none
AT91C_ADC_IDR.name="AT91C_ADC_IDR"
AT91C_ADC_IDR.description="ADC Interrupt Disable Register"
AT91C_ADC_IDR.helpkey="ADC Interrupt Disable Register"
AT91C_ADC_IDR.access=memorymapped
AT91C_ADC_IDR.address=0xFFFD8028
AT91C_ADC_IDR.width=32
AT91C_ADC_IDR.byteEndian=little
AT91C_ADC_IDR.type=enum
AT91C_ADC_IDR.enum.0.name=*** Write only ***
AT91C_ADC_IDR.enum.1.name=Error
AT91C_ADC_CR.name="AT91C_ADC_CR"
AT91C_ADC_CR.description="ADC Control Register"
AT91C_ADC_CR.helpkey="ADC Control Register"
AT91C_ADC_CR.access=memorymapped
AT91C_ADC_CR.address=0xFFFD8000
AT91C_ADC_CR.width=32
AT91C_ADC_CR.byteEndian=little
AT91C_ADC_CR.type=enum
AT91C_ADC_CR.enum.0.name=*** Write only ***
AT91C_ADC_CR.enum.1.name=Error
AT91C_ADC_CDR7.name="AT91C_ADC_CDR7"
AT91C_ADC_CDR7.description="ADC Channel Data Register 7"
AT91C_ADC_CDR7.helpkey="ADC Channel Data Register 7"
AT91C_ADC_CDR7.access=memorymapped
AT91C_ADC_CDR7.address=0xFFFD804C
AT91C_ADC_CDR7.width=32
AT91C_ADC_CDR7.byteEndian=little
AT91C_ADC_CDR7.permission.write=none
AT91C_ADC_CDR6.name="AT91C_ADC_CDR6"
AT91C_ADC_CDR6.description="ADC Channel Data Register 6"
AT91C_ADC_CDR6.helpkey="ADC Channel Data Register 6"
AT91C_ADC_CDR6.access=memorymapped
AT91C_ADC_CDR6.address=0xFFFD8048
AT91C_ADC_CDR6.width=32
AT91C_ADC_CDR6.byteEndian=little
AT91C_ADC_CDR6.permission.write=none
AT91C_ADC_IER.name="AT91C_ADC_IER"
AT91C_ADC_IER.description="ADC Interrupt Enable Register"
AT91C_ADC_IER.helpkey="ADC Interrupt Enable Register"
AT91C_ADC_IER.access=memorymapped
AT91C_ADC_IER.address=0xFFFD8024
AT91C_ADC_IER.width=32
AT91C_ADC_IER.byteEndian=little
AT91C_ADC_IER.type=enum
AT91C_ADC_IER.enum.0.name=*** Write only ***
AT91C_ADC_IER.enum.1.name=Error
AT91C_ADC_CHER.name="AT91C_ADC_CHER"
AT91C_ADC_CHER.description="ADC Channel Enable Register"
AT91C_ADC_CHER.helpkey="ADC Channel Enable Register"
AT91C_ADC_CHER.access=memorymapped
AT91C_ADC_CHER.address=0xFFFD8010
AT91C_ADC_CHER.width=32
AT91C_ADC_CHER.byteEndian=little
AT91C_ADC_CHER.type=enum
AT91C_ADC_CHER.enum.0.name=*** Write only ***
AT91C_ADC_CHER.enum.1.name=Error
AT91C_ADC_CHSR.name="AT91C_ADC_CHSR"
AT91C_ADC_CHSR.description="ADC Channel Status Register"
AT91C_ADC_CHSR.helpkey="ADC Channel Status Register"
AT91C_ADC_CHSR.access=memorymapped
AT91C_ADC_CHSR.address=0xFFFD8018
AT91C_ADC_CHSR.width=32
AT91C_ADC_CHSR.byteEndian=little
AT91C_ADC_CHSR.permission.write=none
AT91C_ADC_MR.name="AT91C_ADC_MR"
AT91C_ADC_MR.description="ADC Mode Register"
AT91C_ADC_MR.helpkey="ADC Mode Register"
AT91C_ADC_MR.access=memorymapped
AT91C_ADC_MR.address=0xFFFD8004
AT91C_ADC_MR.width=32
AT91C_ADC_MR.byteEndian=little
AT91C_ADC_IMR.name="AT91C_ADC_IMR"
AT91C_ADC_IMR.description="ADC Interrupt Mask Register"
AT91C_ADC_IMR.helpkey="ADC Interrupt Mask Register"
AT91C_ADC_IMR.access=memorymapped
AT91C_ADC_IMR.address=0xFFFD802C
AT91C_ADC_IMR.width=32
AT91C_ADC_IMR.byteEndian=little
AT91C_ADC_IMR.permission.write=none
# ========== Group definition for SYS peripheral ========== 
group.SYS.description="ATMEL SYS Registers"
group.SYS.helpkey="ATMEL SYS Registers"
# ========== Group definition for AIC peripheral ========== 
group.AIC.description="ATMEL AIC Registers"
group.AIC.helpkey="ATMEL AIC Registers"
group.AIC.register.0=AT91C_AIC_IVR
group.AIC.register.1=AT91C_AIC_SMR
group.AIC.register.2=AT91C_AIC_FVR
group.AIC.register.3=AT91C_AIC_DCR
group.AIC.register.4=AT91C_AIC_EOICR
group.AIC.register.5=AT91C_AIC_SVR
group.AIC.register.6=AT91C_AIC_FFSR
group.AIC.register.7=AT91C_AIC_ICCR
group.AIC.register.8=AT91C_AIC_ISR
group.AIC.register.9=AT91C_AIC_IMR
group.AIC.register.10=AT91C_AIC_IPR
group.AIC.register.11=AT91C_AIC_FFER
group.AIC.register.12=AT91C_AIC_IECR
group.AIC.register.13=AT91C_AIC_ISCR
group.AIC.register.14=AT91C_AIC_FFDR
group.AIC.register.15=AT91C_AIC_CISR
group.AIC.register.16=AT91C_AIC_IDCR
group.AIC.register.17=AT91C_AIC_SPU
# ========== Group definition for PDC_DBGU peripheral ========== 
group.PDC_DBGU.description="ATMEL PDC_DBGU Registers"
group.PDC_DBGU.helpkey="ATMEL PDC_DBGU Registers"
group.PDC_DBGU.register.0=AT91C_DBGU_TCR
group.PDC_DBGU.register.1=AT91C_DBGU_RNPR
group.PDC_DBGU.register.2=AT91C_DBGU_TNPR
group.PDC_DBGU.register.3=AT91C_DBGU_TPR
group.PDC_DBGU.register.4=AT91C_DBGU_RPR
group.PDC_DBGU.register.5=AT91C_DBGU_RCR
group.PDC_DBGU.register.6=AT91C_DBGU_RNCR
group.PDC_DBGU.register.7=AT91C_DBGU_PTCR
group.PDC_DBGU.register.8=AT91C_DBGU_PTSR
group.PDC_DBGU.register.9=AT91C_DBGU_TNCR
# ========== Group definition for DBGU peripheral ========== 
group.DBGU.description="ATMEL DBGU Registers"
group.DBGU.helpkey="ATMEL DBGU Registers"
group.DBGU.register.0=AT91C_DBGU_EXID
group.DBGU.register.1=AT91C_DBGU_BRGR
group.DBGU.register.2=AT91C_DBGU_IDR
group.DBGU.register.3=AT91C_DBGU_CSR
group.DBGU.register.4=AT91C_DBGU_CIDR
group.DBGU.register.5=AT91C_DBGU_MR
group.DBGU.register.6=AT91C_DBGU_IMR
group.DBGU.register.7=AT91C_DBGU_CR
group.DBGU.register.8=AT91C_DBGU_FNTR
group.DBGU.register.9=AT91C_DBGU_THR
group.DBGU.register.10=AT91C_DBGU_RHR
group.DBGU.register.11=AT91C_DBGU_IER
# ========== Group definition for PIOA peripheral ========== 
group.PIOA.description="ATMEL PIOA Registers"
group.PIOA.helpkey="ATMEL PIOA Registers"
group.PIOA.register.0=AT91C_PIOA_ODR
group.PIOA.register.1=AT91C_PIOA_SODR
group.PIOA.register.2=AT91C_PIOA_ISR
group.PIOA.register.3=AT91C_PIOA_ABSR
group.PIOA.register.4=AT91C_PIOA_IER
group.PIOA.register.5=AT91C_PIOA_PPUDR
group.PIOA.register.6=AT91C_PIOA_IMR
group.PIOA.register.7=AT91C_PIOA_PER
group.PIOA.register.8=AT91C_PIOA_IFDR
group.PIOA.register.9=AT91C_PIOA_OWDR
group.PIOA.register.10=AT91C_PIOA_MDSR
group.PIOA.register.11=AT91C_PIOA_IDR
group.PIOA.register.12=AT91C_PIOA_ODSR
group.PIOA.register.13=AT91C_PIOA_PPUSR
group.PIOA.register.14=AT91C_PIOA_OWSR
group.PIOA.register.15=AT91C_PIOA_BSR
group.PIOA.register.16=AT91C_PIOA_OWER
group.PIOA.register.17=AT91C_PIOA_IFER
group.PIOA.register.18=AT91C_PIOA_PDSR
group.PIOA.register.19=AT91C_PIOA_PPUER
group.PIOA.register.20=AT91C_PIOA_OSR
group.PIOA.register.21=AT91C_PIOA_ASR
group.PIOA.register.22=AT91C_PIOA_MDDR
group.PIOA.register.23=AT91C_PIOA_CODR
group.PIOA.register.24=AT91C_PIOA_MDER
group.PIOA.register.25=AT91C_PIOA_PDR
group.PIOA.register.26=AT91C_PIOA_IFSR
group.PIOA.register.27=AT91C_PIOA_OER
group.PIOA.register.28=AT91C_PIOA_PSR
# ========== Group definition for PIOB peripheral ========== 
group.PIOB.description="ATMEL PIOB Registers"
group.PIOB.helpkey="ATMEL PIOB Registers"
group.PIOB.register.0=AT91C_PIOB_OWDR
group.PIOB.register.1=AT91C_PIOB_MDER
group.PIOB.register.2=AT91C_PIOB_PPUSR
group.PIOB.register.3=AT91C_PIOB_IMR
group.PIOB.register.4=AT91C_PIOB_ASR
group.PIOB.register.5=AT91C_PIOB_PPUDR
group.PIOB.register.6=AT91C_PIOB_PSR
group.PIOB.register.7=AT91C_PIOB_IER
group.PIOB.register.8=AT91C_PIOB_CODR
group.PIOB.register.9=AT91C_PIOB_OWER
group.PIOB.register.10=AT91C_PIOB_ABSR
group.PIOB.register.11=AT91C_PIOB_IFDR
group.PIOB.register.12=AT91C_PIOB_PDSR
group.PIOB.register.13=AT91C_PIOB_IDR
group.PIOB.register.14=AT91C_PIOB_OWSR
group.PIOB.register.15=AT91C_PIOB_PDR
group.PIOB.register.16=AT91C_PIOB_ODR
group.PIOB.register.17=AT91C_PIOB_IFSR
group.PIOB.register.18=AT91C_PIOB_PPUER
group.PIOB.register.19=AT91C_PIOB_SODR
group.PIOB.register.20=AT91C_PIOB_ISR
group.PIOB.register.21=AT91C_PIOB_ODSR
group.PIOB.register.22=AT91C_PIOB_OSR
group.PIOB.register.23=AT91C_PIOB_MDSR
group.PIOB.register.24=AT91C_PIOB_IFER
group.PIOB.register.25=AT91C_PIOB_BSR
group.PIOB.register.26=AT91C_PIOB_MDDR
group.PIOB.register.27=AT91C_PIOB_OER
group.PIOB.register.28=AT91C_PIOB_PER
# ========== Group definition for CKGR peripheral ========== 
group.CKGR.description="ATMEL CKGR Registers"
group.CKGR.helpkey="ATMEL CKGR Registers"
group.CKGR.register.0=AT91C_CKGR_MOR
group.CKGR.register.1=AT91C_CKGR_PLLR
group.CKGR.register.2=AT91C_CKGR_MCFR
# ========== Group definition for PMC peripheral ========== 
group.PMC.description="ATMEL PMC Registers"
group.PMC.helpkey="ATMEL PMC Registers"
group.PMC.register.0=AT91C_PMC_IDR
group.PMC.register.1=AT91C_PMC_MOR
group.PMC.register.2=AT91C_PMC_PLLR
group.PMC.register.3=AT91C_PMC_PCER
group.PMC.register.4=AT91C_PMC_PCKR
group.PMC.register.5=AT91C_PMC_MCKR
group.PMC.register.6=AT91C_PMC_SCDR
group.PMC.register.7=AT91C_PMC_PCDR
group.PMC.register.8=AT91C_PMC_SCSR
group.PMC.register.9=AT91C_PMC_PCSR
group.PMC.register.10=AT91C_PMC_MCFR
group.PMC.register.11=AT91C_PMC_SCER
group.PMC.register.12=AT91C_PMC_IMR
group.PMC.register.13=AT91C_PMC_IER
group.PMC.register.14=AT91C_PMC_SR
# ========== Group definition for RSTC peripheral ========== 
group.RSTC.description="ATMEL RSTC Registers"
group.RSTC.helpkey="ATMEL RSTC Registers"
group.RSTC.register.0=AT91C_RSTC_RCR
group.RSTC.register.1=AT91C_RSTC_RMR
group.RSTC.register.2=AT91C_RSTC_RSR
# ========== Group definition for RTTC peripheral ========== 
group.RTTC.description="ATMEL RTTC Registers"
group.RTTC.helpkey="ATMEL RTTC Registers"
group.RTTC.register.0=AT91C_RTTC_RTSR
group.RTTC.register.1=AT91C_RTTC_RTMR
group.RTTC.register.2=AT91C_RTTC_RTVR
group.RTTC.register.3=AT91C_RTTC_RTAR
# ========== Group definition for PITC peripheral ========== 
group.PITC.description="ATMEL PITC Registers"
group.PITC.helpkey="ATMEL PITC Registers"
group.PITC.register.0=AT91C_PITC_PIVR
group.PITC.register.1=AT91C_PITC_PISR
group.PITC.register.2=AT91C_PITC_PIIR
group.PITC.register.3=AT91C_PITC_PIMR
# ========== Group definition for WDTC peripheral ========== 
group.WDTC.description="ATMEL WDTC Registers"
group.WDTC.helpkey="ATMEL WDTC Registers"
group.WDTC.register.0=AT91C_WDTC_WDCR
group.WDTC.register.1=AT91C_WDTC_WDSR
group.WDTC.register.2=AT91C_WDTC_WDMR
# ========== Group definition for VREG peripheral ========== 
group.VREG.description="ATMEL VREG Registers"
group.VREG.helpkey="ATMEL VREG Registers"
group.VREG.register.0=AT91C_VREG_MR
# ========== Group definition for MC peripheral ========== 
group.MC.description="ATMEL MC Registers"
group.MC.helpkey="ATMEL MC Registers"
group.MC.register.0=AT91C_MC_ASR
group.MC.register.1=AT91C_MC_RCR
group.MC.register.2=AT91C_MC_FCR
group.MC.register.3=AT91C_MC_AASR
group.MC.register.4=AT91C_MC_FSR
group.MC.register.5=AT91C_MC_FMR
# ========== Group definition for PDC_SPI1 peripheral ========== 
group.PDC_SPI1.description="ATMEL PDC_SPI1 Registers"
group.PDC_SPI1.helpkey="ATMEL PDC_SPI1 Registers"
group.PDC_SPI1.register.0=AT91C_SPI1_PTCR
group.PDC_SPI1.register.1=AT91C_SPI1_RPR
group.PDC_SPI1.register.2=AT91C_SPI1_TNCR
group.PDC_SPI1.register.3=AT91C_SPI1_TPR
group.PDC_SPI1.register.4=AT91C_SPI1_TNPR
group.PDC_SPI1.register.5=AT91C_SPI1_TCR
group.PDC_SPI1.register.6=AT91C_SPI1_RCR
group.PDC_SPI1.register.7=AT91C_SPI1_RNPR
group.PDC_SPI1.register.8=AT91C_SPI1_RNCR
group.PDC_SPI1.register.9=AT91C_SPI1_PTSR
# ========== Group definition for SPI1 peripheral ========== 
group.SPI1.description="ATMEL SPI1 Registers"
group.SPI1.helpkey="ATMEL SPI1 Registers"
group.SPI1.register.0=AT91C_SPI1_IMR
group.SPI1.register.1=AT91C_SPI1_IER
group.SPI1.register.2=AT91C_SPI1_MR
group.SPI1.register.3=AT91C_SPI1_RDR
group.SPI1.register.4=AT91C_SPI1_IDR
group.SPI1.register.5=AT91C_SPI1_SR
group.SPI1.register.6=AT91C_SPI1_TDR
group.SPI1.register.7=AT91C_SPI1_CR
group.SPI1.register.8=AT91C_SPI1_CSR
# ========== Group definition for PDC_SPI0 peripheral ========== 
group.PDC_SPI0.description="ATMEL PDC_SPI0 Registers"
group.PDC_SPI0.helpkey="ATMEL PDC_SPI0 Registers"
group.PDC_SPI0.register.0=AT91C_SPI0_PTCR
group.PDC_SPI0.register.1=AT91C_SPI0_TPR
group.PDC_SPI0.register.2=AT91C_SPI0_TCR
group.PDC_SPI0.register.3=AT91C_SPI0_RCR
group.PDC_SPI0.register.4=AT91C_SPI0_PTSR
group.PDC_SPI0.register.5=AT91C_SPI0_RNPR
group.PDC_SPI0.register.6=AT91C_SPI0_RPR
group.PDC_SPI0.register.7=AT91C_SPI0_TNCR
group.PDC_SPI0.register.8=AT91C_SPI0_RNCR
group.PDC_SPI0.register.9=AT91C_SPI0_TNPR
# ========== Group definition for SPI0 peripheral ========== 
group.SPI0.description="ATMEL SPI0 Registers"
group.SPI0.helpkey="ATMEL SPI0 Registers"
group.SPI0.register.0=AT91C_SPI0_IER
group.SPI0.register.1=AT91C_SPI0_SR
group.SPI0.register.2=AT91C_SPI0_IDR
group.SPI0.register.3=AT91C_SPI0_CR
group.SPI0.register.4=AT91C_SPI0_MR
group.SPI0.register.5=AT91C_SPI0_IMR
group.SPI0.register.6=AT91C_SPI0_TDR
group.SPI0.register.7=AT91C_SPI0_RDR
group.SPI0.register.8=AT91C_SPI0_CSR
# ========== Group definition for PDC_US1 peripheral ========== 
group.PDC_US1.description="ATMEL PDC_US1 Registers"
group.PDC_US1.helpkey="ATMEL PDC_US1 Registers"
group.PDC_US1.register.0=AT91C_US1_RNCR
group.PDC_US1.register.1=AT91C_US1_PTCR
group.PDC_US1.register.2=AT91C_US1_TCR
group.PDC_US1.register.3=AT91C_US1_PTSR
group.PDC_US1.register.4=AT91C_US1_TNPR
group.PDC_US1.register.5=AT91C_US1_RCR
group.PDC_US1.register.6=AT91C_US1_RNPR
group.PDC_US1.register.7=AT91C_US1_RPR
group.PDC_US1.register.8=AT91C_US1_TNCR
group.PDC_US1.register.9=AT91C_US1_TPR
# ========== Group definition for US1 peripheral ========== 
group.US1.description="ATMEL US1 Registers"
group.US1.helpkey="ATMEL US1 Registers"
group.US1.register.0=AT91C_US1_IF
group.US1.register.1=AT91C_US1_NER
group.US1.register.2=AT91C_US1_RTOR
group.US1.register.3=AT91C_US1_CSR
group.US1.register.4=AT91C_US1_IDR
group.US1.register.5=AT91C_US1_IER
group.US1.register.6=AT91C_US1_THR
group.US1.register.7=AT91C_US1_TTGR
group.US1.register.8=AT91C_US1_RHR
group.US1.register.9=AT91C_US1_BRGR
group.US1.register.10=AT91C_US1_IMR
group.US1.register.11=AT91C_US1_FIDI
group.US1.register.12=AT91C_US1_CR
group.US1.register.13=AT91C_US1_MR
# ========== Group definition for PDC_US0 peripheral ========== 
group.PDC_US0.description="ATMEL PDC_US0 Registers"
group.PDC_US0.helpkey="ATMEL PDC_US0 Registers"
group.PDC_US0.register.0=AT91C_US0_TNPR
group.PDC_US0.register.1=AT91C_US0_RNPR
group.PDC_US0.register.2=AT91C_US0_TCR
group.PDC_US0.register.3=AT91C_US0_PTCR
group.PDC_US0.register.4=AT91C_US0_PTSR
group.PDC_US0.register.5=AT91C_US0_TNCR
group.PDC_US0.register.6=AT91C_US0_TPR
group.PDC_US0.register.7=AT91C_US0_RCR
group.PDC_US0.register.8=AT91C_US0_RPR
group.PDC_US0.register.9=AT91C_US0_RNCR
# ========== Group definition for US0 peripheral ========== 
group.US0.description="ATMEL US0 Registers"
group.US0.helpkey="ATMEL US0 Registers"
group.US0.register.0=AT91C_US0_BRGR
group.US0.register.1=AT91C_US0_NER
group.US0.register.2=AT91C_US0_CR
group.US0.register.3=AT91C_US0_IMR
group.US0.register.4=AT91C_US0_FIDI
group.US0.register.5=AT91C_US0_TTGR
group.US0.register.6=AT91C_US0_MR
group.US0.register.7=AT91C_US0_RTOR
group.US0.register.8=AT91C_US0_CSR
group.US0.register.9=AT91C_US0_RHR
group.US0.register.10=AT91C_US0_IDR
group.US0.register.11=AT91C_US0_THR
group.US0.register.12=AT91C_US0_IF
group.US0.register.13=AT91C_US0_IER
# ========== Group definition for PDC_SSC peripheral ========== 
group.PDC_SSC.description="ATMEL PDC_SSC Registers"
group.PDC_SSC.helpkey="ATMEL PDC_SSC Registers"
group.PDC_SSC.register.0=AT91C_SSC_TNCR
group.PDC_SSC.register.1=AT91C_SSC_RPR
group.PDC_SSC.register.2=AT91C_SSC_RNCR
group.PDC_SSC.register.3=AT91C_SSC_TPR
group.PDC_SSC.register.4=AT91C_SSC_PTCR
group.PDC_SSC.register.5=AT91C_SSC_TCR
group.PDC_SSC.register.6=AT91C_SSC_RCR
group.PDC_SSC.register.7=AT91C_SSC_RNPR
group.PDC_SSC.register.8=AT91C_SSC_TNPR
group.PDC_SSC.register.9=AT91C_SSC_PTSR
# ========== Group definition for SSC peripheral ========== 
group.SSC.description="ATMEL SSC Registers"
group.SSC.helpkey="ATMEL SSC Registers"
group.SSC.register.0=AT91C_SSC_RHR
group.SSC.register.1=AT91C_SSC_RSHR
group.SSC.register.2=AT91C_SSC_TFMR
group.SSC.register.3=AT91C_SSC_IDR
group.SSC.register.4=AT91C_SSC_THR
group.SSC.register.5=AT91C_SSC_RCMR
group.SSC.register.6=AT91C_SSC_IER
group.SSC.register.7=AT91C_SSC_TSHR
group.SSC.register.8=AT91C_SSC_SR
group.SSC.register.9=AT91C_SSC_CMR
group.SSC.register.10=AT91C_SSC_TCMR
group.SSC.register.11=AT91C_SSC_CR
group.SSC.register.12=AT91C_SSC_IMR
group.SSC.register.13=AT91C_SSC_RFMR
# ========== Group definition for TWI peripheral ========== 
group.TWI.description="ATMEL TWI Registers"
group.TWI.helpkey="ATMEL TWI Registers"
group.TWI.register.0=AT91C_TWI_IER
group.TWI.register.1=AT91C_TWI_CR
group.TWI.register.2=AT91C_TWI_SR
group.TWI.register.3=AT91C_TWI_IMR
group.TWI.register.4=AT91C_TWI_THR
group.TWI.register.5=AT91C_TWI_IDR
group.TWI.register.6=AT91C_TWI_IADR
group.TWI.register.7=AT91C_TWI_MMR
group.TWI.register.8=AT91C_TWI_CWGR
group.TWI.register.9=AT91C_TWI_RHR
# ========== Group definition for PWMC_CH3 peripheral ========== 
group.PWMC_CH3.description="ATMEL PWMC_CH3 Registers"
group.PWMC_CH3.helpkey="ATMEL PWMC_CH3 Registers"
group.PWMC_CH3.register.0=AT91C_PWMC_CH3_CUPDR
group.PWMC_CH3.register.1=AT91C_PWMC_CH3_Reserved
group.PWMC_CH3.register.2=AT91C_PWMC_CH3_CPRDR
group.PWMC_CH3.register.3=AT91C_PWMC_CH3_CDTYR
group.PWMC_CH3.register.4=AT91C_PWMC_CH3_CCNTR
group.PWMC_CH3.register.5=AT91C_PWMC_CH3_CMR
# ========== Group definition for PWMC_CH2 peripheral ========== 
group.PWMC_CH2.description="ATMEL PWMC_CH2 Registers"
group.PWMC_CH2.helpkey="ATMEL PWMC_CH2 Registers"
group.PWMC_CH2.register.0=AT91C_PWMC_CH2_Reserved
group.PWMC_CH2.register.1=AT91C_PWMC_CH2_CMR
group.PWMC_CH2.register.2=AT91C_PWMC_CH2_CCNTR
group.PWMC_CH2.register.3=AT91C_PWMC_CH2_CPRDR
group.PWMC_CH2.register.4=AT91C_PWMC_CH2_CUPDR
group.PWMC_CH2.register.5=AT91C_PWMC_CH2_CDTYR
# ========== Group definition for PWMC_CH1 peripheral ========== 
group.PWMC_CH1.description="ATMEL PWMC_CH1 Registers"
group.PWMC_CH1.helpkey="ATMEL PWMC_CH1 Registers"
group.PWMC_CH1.register.0=AT91C_PWMC_CH1_Reserved
group.PWMC_CH1.register.1=AT91C_PWMC_CH1_CUPDR
group.PWMC_CH1.register.2=AT91C_PWMC_CH1_CPRDR
group.PWMC_CH1.register.3=AT91C_PWMC_CH1_CCNTR
group.PWMC_CH1.register.4=AT91C_PWMC_CH1_CDTYR
group.PWMC_CH1.register.5=AT91C_PWMC_CH1_CMR
# ========== Group definition for PWMC_CH0 peripheral ========== 
group.PWMC_CH0.description="ATMEL PWMC_CH0 Registers"
group.PWMC_CH0.helpkey="ATMEL PWMC_CH0 Registers"
group.PWMC_CH0.register.0=AT91C_PWMC_CH0_Reserved
group.PWMC_CH0.register.1=AT91C_PWMC_CH0_CPRDR
group.PWMC_CH0.register.2=AT91C_PWMC_CH0_CDTYR
group.PWMC_CH0.register.3=AT91C_PWMC_CH0_CMR
group.PWMC_CH0.register.4=AT91C_PWMC_CH0_CUPDR
group.PWMC_CH0.register.5=AT91C_PWMC_CH0_CCNTR
# ========== Group definition for PWMC peripheral ========== 
group.PWMC.description="ATMEL PWMC Registers"
group.PWMC.helpkey="ATMEL PWMC Registers"
group.PWMC.register.0=AT91C_PWMC_IDR
group.PWMC.register.1=AT91C_PWMC_DIS
group.PWMC.register.2=AT91C_PWMC_IER
group.PWMC.register.3=AT91C_PWMC_VR
group.PWMC.register.4=AT91C_PWMC_ISR
group.PWMC.register.5=AT91C_PWMC_SR
group.PWMC.register.6=AT91C_PWMC_IMR
group.PWMC.register.7=AT91C_PWMC_MR
group.PWMC.register.8=AT91C_PWMC_ENA
# ========== Group definition for UDP peripheral ========== 
group.UDP.description="ATMEL UDP Registers"
group.UDP.helpkey="ATMEL UDP Registers"
group.UDP.register.0=AT91C_UDP_IMR
group.UDP.register.1=AT91C_UDP_FADDR
group.UDP.register.2=AT91C_UDP_NUM
group.UDP.register.3=AT91C_UDP_FDR
group.UDP.register.4=AT91C_UDP_ISR
group.UDP.register.5=AT91C_UDP_CSR
group.UDP.register.6=AT91C_UDP_IDR
group.UDP.register.7=AT91C_UDP_ICR
group.UDP.register.8=AT91C_UDP_RSTEP
group.UDP.register.9=AT91C_UDP_TXVC
group.UDP.register.10=AT91C_UDP_GLBSTATE
group.UDP.register.11=AT91C_UDP_IER
# ========== Group definition for TC0 peripheral ========== 
group.TC0.description="ATMEL TC0 Registers"
group.TC0.helpkey="ATMEL TC0 Registers"
group.TC0.register.0=AT91C_TC0_SR
group.TC0.register.1=AT91C_TC0_RC
group.TC0.register.2=AT91C_TC0_RB
group.TC0.register.3=AT91C_TC0_CCR
group.TC0.register.4=AT91C_TC0_CMR
group.TC0.register.5=AT91C_TC0_IER
group.TC0.register.6=AT91C_TC0_RA
group.TC0.register.7=AT91C_TC0_IDR
group.TC0.register.8=AT91C_TC0_CV
group.TC0.register.9=AT91C_TC0_IMR
# ========== Group definition for TC1 peripheral ========== 
group.TC1.description="ATMEL TC1 Registers"
group.TC1.helpkey="ATMEL TC1 Registers"
group.TC1.register.0=AT91C_TC1_RB
group.TC1.register.1=AT91C_TC1_CCR
group.TC1.register.2=AT91C_TC1_IER
group.TC1.register.3=AT91C_TC1_IDR
group.TC1.register.4=AT91C_TC1_SR
group.TC1.register.5=AT91C_TC1_CMR
group.TC1.register.6=AT91C_TC1_RA
group.TC1.register.7=AT91C_TC1_RC
group.TC1.register.8=AT91C_TC1_IMR
group.TC1.register.9=AT91C_TC1_CV
# ========== Group definition for TC2 peripheral ========== 
group.TC2.description="ATMEL TC2 Registers"
group.TC2.helpkey="ATMEL TC2 Registers"
group.TC2.register.0=AT91C_TC2_CMR
group.TC2.register.1=AT91C_TC2_CCR
group.TC2.register.2=AT91C_TC2_CV
group.TC2.register.3=AT91C_TC2_RA
group.TC2.register.4=AT91C_TC2_RB
group.TC2.register.5=AT91C_TC2_IDR
group.TC2.register.6=AT91C_TC2_IMR
group.TC2.register.7=AT91C_TC2_RC
group.TC2.register.8=AT91C_TC2_IER
group.TC2.register.9=AT91C_TC2_SR
# ========== Group definition for TCB peripheral ========== 
group.TCB.description="ATMEL TCB Registers"
group.TCB.helpkey="ATMEL TCB Registers"
group.TCB.register.0=AT91C_TCB_BMR
group.TCB.register.1=AT91C_TCB_BCR
# ========== Group definition for CAN_MB0 peripheral ========== 
group.CAN_MB0.description="ATMEL CAN_MB0 Registers"
group.CAN_MB0.helpkey="ATMEL CAN_MB0 Registers"
group.CAN_MB0.register.0=AT91C_CAN_MB0_MDL
group.CAN_MB0.register.1=AT91C_CAN_MB0_MAM
group.CAN_MB0.register.2=AT91C_CAN_MB0_MCR
group.CAN_MB0.register.3=AT91C_CAN_MB0_MID
group.CAN_MB0.register.4=AT91C_CAN_MB0_MSR
group.CAN_MB0.register.5=AT91C_CAN_MB0_MFID
group.CAN_MB0.register.6=AT91C_CAN_MB0_MDH
group.CAN_MB0.register.7=AT91C_CAN_MB0_MMR
# ========== Group definition for CAN_MB1 peripheral ========== 
group.CAN_MB1.description="ATMEL CAN_MB1 Registers"
group.CAN_MB1.helpkey="ATMEL CAN_MB1 Registers"
group.CAN_MB1.register.0=AT91C_CAN_MB1_MDL
group.CAN_MB1.register.1=AT91C_CAN_MB1_MID
group.CAN_MB1.register.2=AT91C_CAN_MB1_MMR
group.CAN_MB1.register.3=AT91C_CAN_MB1_MSR
group.CAN_MB1.register.4=AT91C_CAN_MB1_MAM
group.CAN_MB1.register.5=AT91C_CAN_MB1_MDH
group.CAN_MB1.register.6=AT91C_CAN_MB1_MCR
group.CAN_MB1.register.7=AT91C_CAN_MB1_MFID
# ========== Group definition for CAN_MB2 peripheral ========== 
group.CAN_MB2.description="ATMEL CAN_MB2 Registers"
group.CAN_MB2.helpkey="ATMEL CAN_MB2 Registers"
group.CAN_MB2.register.0=AT91C_CAN_MB2_MCR
group.CAN_MB2.register.1=AT91C_CAN_MB2_MDH
group.CAN_MB2.register.2=AT91C_CAN_MB2_MID
group.CAN_MB2.register.3=AT91C_CAN_MB2_MDL
group.CAN_MB2.register.4=AT91C_CAN_MB2_MMR
group.CAN_MB2.register.5=AT91C_CAN_MB2_MAM
group.CAN_MB2.register.6=AT91C_CAN_MB2_MFID
group.CAN_MB2.register.7=AT91C_CAN_MB2_MSR
# ========== Group definition for CAN_MB3 peripheral ========== 
group.CAN_MB3.description="ATMEL CAN_MB3 Registers"
group.CAN_MB3.helpkey="ATMEL CAN_MB3 Registers"
group.CAN_MB3.register.0=AT91C_CAN_MB3_MFID
group.CAN_MB3.register.1=AT91C_CAN_MB3_MAM
group.CAN_MB3.register.2=AT91C_CAN_MB3_MID
group.CAN_MB3.register.3=AT91C_CAN_MB3_MCR
group.CAN_MB3.register.4=AT91C_CAN_MB3_MMR
group.CAN_MB3.register.5=AT91C_CAN_MB3_MSR
group.CAN_MB3.register.6=AT91C_CAN_MB3_MDL
group.CAN_MB3.register.7=AT91C_CAN_MB3_MDH
# ========== Group definition for CAN_MB4 peripheral ========== 
group.CAN_MB4.description="ATMEL CAN_MB4 Registers"
group.CAN_MB4.helpkey="ATMEL CAN_MB4 Registers"
group.CAN_MB4.register.0=AT91C_CAN_MB4_MID
group.CAN_MB4.register.1=AT91C_CAN_MB4_MMR
group.CAN_MB4.register.2=AT91C_CAN_MB4_MDH
group.CAN_MB4.register.3=AT91C_CAN_MB4_MFID
group.CAN_MB4.register.4=AT91C_CAN_MB4_MSR
group.CAN_MB4.register.5=AT91C_CAN_MB4_MCR
group.CAN_MB4.register.6=AT91C_CAN_MB4_MDL
group.CAN_MB4.register.7=AT91C_CAN_MB4_MAM
# ========== Group definition for CAN_MB5 peripheral ========== 
group.CAN_MB5.description="ATMEL CAN_MB5 Registers"
group.CAN_MB5.helpkey="ATMEL CAN_MB5 Registers"
group.CAN_MB5.register.0=AT91C_CAN_MB5_MSR
group.CAN_MB5.register.1=AT91C_CAN_MB5_MCR
group.CAN_MB5.register.2=AT91C_CAN_MB5_MFID
group.CAN_MB5.register.3=AT91C_CAN_MB5_MDH
group.CAN_MB5.register.4=AT91C_CAN_MB5_MID
group.CAN_MB5.register.5=AT91C_CAN_MB5_MMR
group.CAN_MB5.register.6=AT91C_CAN_MB5_MDL
group.CAN_MB5.register.7=AT91C_CAN_MB5_MAM
# ========== Group definition for CAN_MB6 peripheral ========== 
group.CAN_MB6.description="ATMEL CAN_MB6 Registers"
group.CAN_MB6.helpkey="ATMEL CAN_MB6 Registers"
group.CAN_MB6.register.0=AT91C_CAN_MB6_MFID
group.CAN_MB6.register.1=AT91C_CAN_MB6_MID
group.CAN_MB6.register.2=AT91C_CAN_MB6_MAM
group.CAN_MB6.register.3=AT91C_CAN_MB6_MSR
group.CAN_MB6.register.4=AT91C_CAN_MB6_MDL
group.CAN_MB6.register.5=AT91C_CAN_MB6_MCR
group.CAN_MB6.register.6=AT91C_CAN_MB6_MDH
group.CAN_MB6.register.7=AT91C_CAN_MB6_MMR
# ========== Group definition for CAN_MB7 peripheral ========== 
group.CAN_MB7.description="ATMEL CAN_MB7 Registers"
group.CAN_MB7.helpkey="ATMEL CAN_MB7 Registers"
group.CAN_MB7.register.0=AT91C_CAN_MB7_MCR
group.CAN_MB7.register.1=AT91C_CAN_MB7_MDH
group.CAN_MB7.register.2=AT91C_CAN_MB7_MFID
group.CAN_MB7.register.3=AT91C_CAN_MB7_MDL
group.CAN_MB7.register.4=AT91C_CAN_MB7_MID
group.CAN_MB7.register.5=AT91C_CAN_MB7_MMR
group.CAN_MB7.register.6=AT91C_CAN_MB7_MAM
group.CAN_MB7.register.7=AT91C_CAN_MB7_MSR
# ========== Group definition for CAN peripheral ========== 
group.CAN.description="ATMEL CAN Registers"
group.CAN.helpkey="ATMEL CAN Registers"
group.CAN.register.0=AT91C_CAN_TCR
group.CAN.register.1=AT91C_CAN_IMR
group.CAN.register.2=AT91C_CAN_IER
group.CAN.register.3=AT91C_CAN_ECR
group.CAN.register.4=AT91C_CAN_TIMESTP
group.CAN.register.5=AT91C_CAN_MR
group.CAN.register.6=AT91C_CAN_IDR
group.CAN.register.7=AT91C_CAN_ACR
group.CAN.register.8=AT91C_CAN_TIM
group.CAN.register.9=AT91C_CAN_SR
group.CAN.register.10=AT91C_CAN_BR
group.CAN.register.11=AT91C_CAN_VR
# ========== Group definition for EMAC peripheral ========== 
group.EMAC.description="ATMEL EMAC Registers"
group.EMAC.helpkey="ATMEL EMAC Registers"
group.EMAC.register.0=AT91C_EMAC_ISR
group.EMAC.register.1=AT91C_EMAC_SA4H
group.EMAC.register.2=AT91C_EMAC_SA1L
group.EMAC.register.3=AT91C_EMAC_ELE
group.EMAC.register.4=AT91C_EMAC_LCOL
group.EMAC.register.5=AT91C_EMAC_RLE
group.EMAC.register.6=AT91C_EMAC_WOL
group.EMAC.register.7=AT91C_EMAC_DTF
group.EMAC.register.8=AT91C_EMAC_TUND
group.EMAC.register.9=AT91C_EMAC_NCR
group.EMAC.register.10=AT91C_EMAC_SA4L
group.EMAC.register.11=AT91C_EMAC_RSR
group.EMAC.register.12=AT91C_EMAC_SA3L
group.EMAC.register.13=AT91C_EMAC_TSR
group.EMAC.register.14=AT91C_EMAC_IDR
group.EMAC.register.15=AT91C_EMAC_RSE
group.EMAC.register.16=AT91C_EMAC_ECOL
group.EMAC.register.17=AT91C_EMAC_TID
group.EMAC.register.18=AT91C_EMAC_HRB
group.EMAC.register.19=AT91C_EMAC_TBQP
group.EMAC.register.20=AT91C_EMAC_USRIO
group.EMAC.register.21=AT91C_EMAC_PTR
group.EMAC.register.22=AT91C_EMAC_SA2H
group.EMAC.register.23=AT91C_EMAC_ROV
group.EMAC.register.24=AT91C_EMAC_ALE
group.EMAC.register.25=AT91C_EMAC_RJA
group.EMAC.register.26=AT91C_EMAC_RBQP
group.EMAC.register.27=AT91C_EMAC_TPF
group.EMAC.register.28=AT91C_EMAC_NCFGR
group.EMAC.register.29=AT91C_EMAC_HRT
group.EMAC.register.30=AT91C_EMAC_USF
group.EMAC.register.31=AT91C_EMAC_FCSE
group.EMAC.register.32=AT91C_EMAC_TPQ
group.EMAC.register.33=AT91C_EMAC_MAN
group.EMAC.register.34=AT91C_EMAC_FTO
group.EMAC.register.35=AT91C_EMAC_REV
group.EMAC.register.36=AT91C_EMAC_IMR
group.EMAC.register.37=AT91C_EMAC_SCF
group.EMAC.register.38=AT91C_EMAC_PFR
group.EMAC.register.39=AT91C_EMAC_MCF
group.EMAC.register.40=AT91C_EMAC_NSR
group.EMAC.register.41=AT91C_EMAC_SA2L
group.EMAC.register.42=AT91C_EMAC_FRO
group.EMAC.register.43=AT91C_EMAC_IER
group.EMAC.register.44=AT91C_EMAC_SA1H
group.EMAC.register.45=AT91C_EMAC_CSE
group.EMAC.register.46=AT91C_EMAC_SA3H
group.EMAC.register.47=AT91C_EMAC_RRE
group.EMAC.register.48=AT91C_EMAC_STE
# ========== Group definition for PDC_ADC peripheral ========== 
group.PDC_ADC.description="ATMEL PDC_ADC Registers"
group.PDC_ADC.helpkey="ATMEL PDC_ADC Registers"
group.PDC_ADC.register.0=AT91C_ADC_PTSR
group.PDC_ADC.register.1=AT91C_ADC_PTCR
group.PDC_ADC.register.2=AT91C_ADC_TNPR
group.PDC_ADC.register.3=AT91C_ADC_TNCR
group.PDC_ADC.register.4=AT91C_ADC_RNPR
group.PDC_ADC.register.5=AT91C_ADC_RNCR
group.PDC_ADC.register.6=AT91C_ADC_RPR
group.PDC_ADC.register.7=AT91C_ADC_TCR
group.PDC_ADC.register.8=AT91C_ADC_TPR
group.PDC_ADC.register.9=AT91C_ADC_RCR
# ========== Group definition for ADC peripheral ========== 
group.ADC.description="ATMEL ADC Registers"
group.ADC.helpkey="ATMEL ADC Registers"
group.ADC.register.0=AT91C_ADC_CDR2
group.ADC.register.1=AT91C_ADC_CDR3
group.ADC.register.2=AT91C_ADC_CDR0
group.ADC.register.3=AT91C_ADC_CDR5
group.ADC.register.4=AT91C_ADC_CHDR
group.ADC.register.5=AT91C_ADC_SR
group.ADC.register.6=AT91C_ADC_CDR4
group.ADC.register.7=AT91C_ADC_CDR1
group.ADC.register.8=AT91C_ADC_LCDR
group.ADC.register.9=AT91C_ADC_IDR
group.ADC.register.10=AT91C_ADC_CR
group.ADC.register.11=AT91C_ADC_CDR7
group.ADC.register.12=AT91C_ADC_CDR6
group.ADC.register.13=AT91C_ADC_IER
group.ADC.register.14=AT91C_ADC_CHER
group.ADC.register.15=AT91C_ADC_CHSR
group.ADC.register.16=AT91C_ADC_MR
group.ADC.register.17=AT91C_ADC_IMR
group.AT91SAM7X256.description="ATMEL AT91SAM7X256 Registers"
group.AT91SAM7X256.helpkey="ATMEL AT91SAM7X256 Registers"
group.AT91SAM7X256.topLevelIndex=100 
group.AT91SAM7X256.group.0=SYS
group.AT91SAM7X256.group.1=AIC
group.AT91SAM7X256.group.2=PDC_DBGU
group.AT91SAM7X256.group.3=DBGU
group.AT91SAM7X256.group.4=PIOA
group.AT91SAM7X256.group.5=PIOB
group.AT91SAM7X256.group.6=CKGR
group.AT91SAM7X256.group.7=PMC
group.AT91SAM7X256.group.8=RSTC
group.AT91SAM7X256.group.9=RTTC
group.AT91SAM7X256.group.10=PITC
group.AT91SAM7X256.group.11=WDTC
group.AT91SAM7X256.group.12=VREG
group.AT91SAM7X256.group.13=MC
group.AT91SAM7X256.group.14=PDC_SPI1
group.AT91SAM7X256.group.15=SPI1
group.AT91SAM7X256.group.16=PDC_SPI0
group.AT91SAM7X256.group.17=SPI0
group.AT91SAM7X256.group.18=PDC_US1
group.AT91SAM7X256.group.19=US1
group.AT91SAM7X256.group.20=PDC_US0
group.AT91SAM7X256.group.21=US0
group.AT91SAM7X256.group.22=PDC_SSC
group.AT91SAM7X256.group.23=SSC
group.AT91SAM7X256.group.24=TWI
group.AT91SAM7X256.group.25=PWMC_CH3
group.AT91SAM7X256.group.26=PWMC_CH2
group.AT91SAM7X256.group.27=PWMC_CH1
group.AT91SAM7X256.group.28=PWMC_CH0
group.AT91SAM7X256.group.29=PWMC
group.AT91SAM7X256.group.30=UDP
group.AT91SAM7X256.group.31=TC0
group.AT91SAM7X256.group.32=TC1
group.AT91SAM7X256.group.33=TC2
group.AT91SAM7X256.group.34=TCB
group.AT91SAM7X256.group.35=CAN_MB0
group.AT91SAM7X256.group.36=CAN_MB1
group.AT91SAM7X256.group.37=CAN_MB2
group.AT91SAM7X256.group.38=CAN_MB3
group.AT91SAM7X256.group.39=CAN_MB4
group.AT91SAM7X256.group.40=CAN_MB5
group.AT91SAM7X256.group.41=CAN_MB6
group.AT91SAM7X256.group.42=CAN_MB7
group.AT91SAM7X256.group.43=CAN
group.AT91SAM7X256.group.44=EMAC
group.AT91SAM7X256.group.45=PDC_ADC
group.AT91SAM7X256.group.46=ADC
