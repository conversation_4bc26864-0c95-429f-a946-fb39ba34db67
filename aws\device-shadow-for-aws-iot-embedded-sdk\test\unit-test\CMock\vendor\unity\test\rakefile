# ==========================================
#   Unity Project - A Test Framework for C
#   Copyright (c) 2007 <PERSON>, <PERSON>, <PERSON>
#   [Released under MIT License. Please refer to license.txt for details]
# ==========================================

$verbose = false
$extra_paths = []

require 'rake'
require 'rake/clean'
require_relative 'rakefile_helper'
require 'rspec/core/rake_task'

TEMP_DIRS = [
	File.join(__dir__, 'build'),
	File.join(__dir__, 'sandbox')
]

TEMP_DIRS.each do |dir|
  directory(dir)
  CLOBBER.include(dir)
end

task :prepare_for_tests => TEMP_DIRS

include RakefileHelpers

# Load proper GCC as defult configuration
DEFAULT_CONFIG_FILE = 'gcc_auto_stdint.yml'
configure_toolchain(DEFAULT_CONFIG_FILE)

############# ALL THE SELF-TESTS WE CAN PERFORM
namespace :test do
  desc "Build and test Unity"
  task :all => [:clean, :prepare_for_tests, 'test:scripts', 'test:unit', :style, 'test:fixture', 'test:memory', 'test:summary']
  task :ci => [:clean, :prepare_for_tests, 'test:scripts', 'test:unit', :style, 'test:make', 'test:fixture', 'test:memory', 'test:summary']

  desc "Test unity with its own unit tests"
  task :unit => [:prepare_for_tests] do
    run_tests unit_test_files
  end

  namespace :unit do
    unit_test_files.each do |f|
      desc "test this unit only"
      task File.basename(f,'.c').sub('test_unity_','') => [:prepare_for_tests] do
        run_tests [f]
      end
    end
  end

  desc "Test unity's helper scripts"
  task :scripts => [:prepare_for_tests] do
    Dir['tests/test_*.rb'].each do |scriptfile|
      require "./"+scriptfile
    end
  end

  desc "Test unity triggered from make"
  task :make => [:prepare_for_tests] do
    run_make_tests()
  end

  desc "Test unity fixture addon"
  task :fixture => [:prepare_for_tests] do
    test_fixtures()
  end

  desc "Test unity memory addon"
  task :memory => [:prepare_for_tests] do
    test_memory()
  end

  desc "Test unity examples"
  task :examples => [:prepare_for_tests] do
    execute("cd ../examples/example_1 && make -s ci", false)
    execute("cd ../examples/example_2 && make -s ci", false)
    execute("cd ../examples/example_3 && rake", false)
  end

  desc "Run all rspecs"
  RSpec::Core::RakeTask.new(:spec) do |t|
    t.pattern = 'spec/**/*_spec.rb'
  end

  desc "Generate test summary"
  task :summary do
    report_summary
  end
end

###################### Shorthand for many common tasks
task :ci => ['test:ci']
task :all => ['test:all']
task :default => [:clobber, :all]

desc "Load configuration"
task :config, :config_file do |t, args|
  configure_toolchain(args[:config_file])
end

task :no_color do
  $colour_output = false
end

task :verbose do
  $verbose = true
end

################### CODING STYLE VALIDATION
namespace :style do
  desc "Check style"
  task :check do
    report "\nVERIFYING RUBY STYLE"
    report execute("rubocop ../auto ../examples ../extras --config .rubocop.yml", true)
    report "Styling Ruby:PASS"
  end

  namespace :check do
    Dir['../**/*.rb'].each do |f|
      filename = File.basename(f, '.rb')
      #desc "Check Style of #{filename}"
      task filename.to_sym => ['style:clean'] do
        report execute("rubocop #{f} --color --config .rubocop.yml", true)
        report "Style Checked for #{f}"
      end
    end
  end

  desc "Fix Style of all C Code"
  task :c do
    run_astyle("../src/*.* ../extras/fixture/src/*.*")
  end

  namespace :c do
    Dir['../{src,extras/**}/*.{c,h}'].each do |f|
      filename = File.basename(f)[0..-3]
      #desc "Check Style of #{filename}"
      task filename.to_sym do
        run_astyle f
      end
    end
  end

  desc "Attempt to Autocorrect style"
  task :auto  => ['style:clean'] do
    execute("rubocop ../auto ../examples ../extras --auto-correct --config .rubocop.yml")
    report "Autocorrected What We Could."
  end

  desc "Update style todo list"
  task :todo  => ['style:clean'] do
    execute("rubocop ../auto ../examples ../extras --auto-gen-config --config .rubocop.yml")
    report "Updated Style TODO List."
  end

  task :clean do
    File.delete(".rubocop_todo.yml") if File.exists?(".rubocop_todo.yml")
  end
end

task :style => ['style:check']
