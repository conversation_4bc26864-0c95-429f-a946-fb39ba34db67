// MISRA C-2012 Rules

{
    version : "2.0",
    standard : "c2012",
    title: "Coverity MISRA Configuration",
    deviations : [
        // Disable the following rules.
        {
            deviation: "Rule 2.4",
            reason: "Allow unused tags. Some compilers warn if types are not tagged."
        },
        {
            deviation: "Rule 2.5",
            reason: "Allow unused macros. Library headers may define macros intended for the application's use, but not used by a specific file."
        },
        {
            deviation: "Rule 3.1",
            reason: "Allow nested comments. Documentation blocks contain comments for example code."
        },
        {
            deviation: "Rule 8.7",
            reason: "API functions are not used by library. They must be externally visible in order to be used by the application."
        },
    ]
}
