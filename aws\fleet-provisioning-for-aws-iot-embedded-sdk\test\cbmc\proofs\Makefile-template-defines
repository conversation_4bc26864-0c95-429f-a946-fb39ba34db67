
# Absolute path to the root of the source tree.
#
SRCDIR ?= $(abspath $(PROOF_ROOT)/../../..)


# Absolute path to the litani script.
#
LITANI ?= litani


# Name of this proof project, displayed in proof reports. For example,
# "s2n" or "Amazon FreeRTOS". For projects with multiple proof roots,
# this may be overridden on the command-line to Make, for example
#
# 	  make PROJECT_NAME="FreeRTOS MQTT" report
#
PROJECT_NAME = "Fleet-Provisioning-for-AWS-IoT-embedded-sdk"

